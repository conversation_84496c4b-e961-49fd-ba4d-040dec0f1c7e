"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"

// 類型定義
export interface LibraryCategory {
  id: string
  name: string
  count: number
}

export interface LibraryCollection {
  id: string
  name: string
  itemCount: number
  categoryId?: string
}

export interface LibraryTag {
  id: number
  name: string
  count: number
}

export interface BookmarkedItem {
  id: string
  contentType: "viewpoint" | "thread"
  title: string
  content?: string
  author: string
  timestamp: string
  topics: string[]
  subtopics?: string[]
  tags?: string[]
  stats: {
    likes: number
    dislikes?: number
    comments?: number
    replies?: number
    bookmarks: number
    hasLiked: boolean
    hasDisliked: boolean
    hasBookmarked: boolean
  }
  collectionIds: string[]
  contribution_type?: string
  originalAuthor?: string
  originalSource?: string
}

// Context 狀態類型
interface LibraryContextState {
  // 側邊欄數據
  categories: LibraryCategory[]
  collections: LibraryCollection[]
  tags: LibraryTag[]
  
  // 收藏項目數據
  bookmarkedItems: BookmarkedItem[]
  
  // 載入狀態
  isLoading: {
    categories: boolean
    collections: boolean
    tags: boolean
    bookmarks: boolean
  }
  
  // 數據是否已載入
  isDataLoaded: {
    categories: boolean
    collections: boolean
    tags: boolean
    bookmarks: boolean
  }
}

// Context 操作類型
interface LibraryContextActions {
  // 設置數據
  setCategories: (categories: LibraryCategory[]) => void
  setCollections: (collections: LibraryCollection[] | ((prev: LibraryCollection[]) => LibraryCollection[])) => void
  setTags: (tags: LibraryTag[]) => void
  setBookmarkedItems: (items: BookmarkedItem[] | ((prev: BookmarkedItem[]) => BookmarkedItem[])) => void
  
  // 設置載入狀態
  setLoading: (key: keyof LibraryContextState['isLoading'], loading: boolean) => void
  
  // 標記數據已載入
  markDataLoaded: (key: keyof LibraryContextState['isDataLoaded']) => void
  
  // 清除所有數據
  clearAllData: () => void
  
  // 從 sessionStorage 載入數據
  loadFromStorage: () => void
  
  // 保存數據到 sessionStorage
  saveToStorage: () => void
}

type LibraryContextType = LibraryContextState & LibraryContextActions

// 初始狀態
const initialState: LibraryContextState = {
  categories: [],
  collections: [],
  tags: [],
  bookmarkedItems: [],
  isLoading: {
    categories: false,
    collections: false,
    tags: false,
    bookmarks: false
  },
  isDataLoaded: {
    categories: false,
    collections: false,
    tags: false,
    bookmarks: false
  }
}

// 創建 Context
const LibraryContext = createContext<LibraryContextType | null>(null)

// sessionStorage 鍵名
const STORAGE_KEYS = {
  categories: 'library_categories',
  collections: 'library_collections',
  tags: 'library_tags',
  bookmarks: 'library_bookmarks',
  dataLoaded: 'library_data_loaded'
}

// Provider 組件
export function LibraryProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<LibraryContextState>(initialState)

  // 設置數據的輔助函數
  const setCategories = (categories: LibraryCategory[]) => {
    setState(prev => ({ ...prev, categories }))
  }

  const setCollections = (collections: LibraryCollection[] | ((prev: LibraryCollection[]) => LibraryCollection[])) => {
    setState(prev => ({
      ...prev,
      collections: typeof collections === 'function' ? collections(prev.collections) : collections
    }))
  }

  const setTags = (tags: LibraryTag[]) => {
    setState(prev => ({ ...prev, tags }))
  }

  const setBookmarkedItems = (items: BookmarkedItem[] | ((prev: BookmarkedItem[]) => BookmarkedItem[])) => {
    setState(prev => ({
      ...prev,
      bookmarkedItems: typeof items === 'function' ? items(prev.bookmarkedItems) : items
    }))
  }

  const setLoading = (key: keyof LibraryContextState['isLoading'], loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: { ...prev.isLoading, [key]: loading }
    }))
  }

  const markDataLoaded = (key: keyof LibraryContextState['isDataLoaded']) => {
    setState(prev => ({
      ...prev,
      isDataLoaded: { ...prev.isDataLoaded, [key]: true }
    }))
  }

  const clearAllData = () => {
    setState(initialState)
    // 清除 sessionStorage
    Object.values(STORAGE_KEYS).forEach(key => {
      sessionStorage.removeItem(key)
    })
  }

  // 從 sessionStorage 載入數據
  const loadFromStorage = () => {
    try {
      const categories = sessionStorage.getItem(STORAGE_KEYS.categories)
      const collections = sessionStorage.getItem(STORAGE_KEYS.collections)
      const tags = sessionStorage.getItem(STORAGE_KEYS.tags)
      const bookmarks = sessionStorage.getItem(STORAGE_KEYS.bookmarks)
      const dataLoaded = sessionStorage.getItem(STORAGE_KEYS.dataLoaded)

      if (categories) {
        setState(prev => ({ ...prev, categories: JSON.parse(categories) }))
      }
      if (collections) {
        setState(prev => ({ ...prev, collections: JSON.parse(collections) }))
      }
      if (tags) {
        setState(prev => ({ ...prev, tags: JSON.parse(tags) }))
      }
      if (bookmarks) {
        setState(prev => ({ ...prev, bookmarkedItems: JSON.parse(bookmarks) }))
      }
      if (dataLoaded) {
        setState(prev => ({ ...prev, isDataLoaded: JSON.parse(dataLoaded) }))
      }
    } catch (error) {
      console.error('Error loading from sessionStorage:', error)
    }
  }

  // 保存數據到 sessionStorage
  const saveToStorage = () => {
    try {
      sessionStorage.setItem(STORAGE_KEYS.categories, JSON.stringify(state.categories))
      sessionStorage.setItem(STORAGE_KEYS.collections, JSON.stringify(state.collections))
      sessionStorage.setItem(STORAGE_KEYS.tags, JSON.stringify(state.tags))
      sessionStorage.setItem(STORAGE_KEYS.bookmarks, JSON.stringify(state.bookmarkedItems))
      sessionStorage.setItem(STORAGE_KEYS.dataLoaded, JSON.stringify(state.isDataLoaded))
    } catch (error) {
      console.error('Error saving to sessionStorage:', error)
    }
  }

  // 組件掛載時從 sessionStorage 載入數據
  useEffect(() => {
    loadFromStorage()
  }, [])

  // 數據變化時自動保存到 sessionStorage
  useEffect(() => {
    if (state.categories.length > 0 || state.collections.length > 0 || 
        state.tags.length > 0 || state.bookmarkedItems.length > 0) {
      saveToStorage()
    }
  }, [state.categories, state.collections, state.tags, state.bookmarkedItems, state.isDataLoaded])

  const contextValue: LibraryContextType = {
    ...state,
    setCategories,
    setCollections,
    setTags,
    setBookmarkedItems,
    setLoading,
    markDataLoaded,
    clearAllData,
    loadFromStorage,
    saveToStorage
  }

  return (
    <LibraryContext.Provider value={contextValue}>
      {children}
    </LibraryContext.Provider>
  )
}

// Hook 來使用 Context
export function useLibrary() {
  const context = useContext(LibraryContext)
  if (!context) {
    throw new Error('useLibrary must be used within a LibraryProvider')
  }
  return context
}

// 導出類型供其他組件使用
export type { LibraryContextType }
