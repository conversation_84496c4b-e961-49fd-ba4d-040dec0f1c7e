# 權限系統文檔

## 概述

本系統實現了一個基於角色的權限控制系統 (RBAC)，支援站長、主題版主、子主題版主等不同層級的管理權限。

## 角色層級

```
站長 (owner) - 100級
├── 擁有所有系統權限
├── 可以管理所有內容和用戶
└── 可以指派和移除版主

主題版主 (topic_mod) - 50級
├── 管理特定主題下的所有內容
├── 可以審核和發布內容
└── 可以管理子主題和子主題版主

子主題版主 (subtopic_mod) - 25級
├── 管理特定子主題下的內容
├── 可以審核和發布內容
└── 基本內容管理權限

一般用戶 - 0級
└── 基本使用權限
```

## 權限列表

### 內容管理權限

- `manage_all_content` - 管理所有內容（站長）
- `manage_topic_content` - 管理主題內容（主題版主）
- `manage_subtopic_content` - 管理子主題內容（子主題版主）
- `approve_content` - 審核內容
- `reject_content` - 拒絕內容
- `delete_content` - 刪除內容
- `edit_others_content` - 編輯他人內容

### 用戶管理權限

- `manage_moderators` - 管理版主（站長）
- `manage_users` - 管理用戶（站長）
- `ban_users` - 封禁用戶（站長）

### 系統管理權限

- `manage_topics` - 管理主題（站長）
- `manage_subtopics` - 管理子主題（站長、主題版主）
- `view_admin_panel` - 查看管理面板
- `manage_system_settings` - 管理系統設定（站長）

## 數據庫結構

### 核心表格

1. **roles** - 角色定義
2. **permissions** - 權限定義
3. **role_permissions** - 角色權限關聯
4. **profile_roles** - 用戶角色關聯
5. **topic_moderators** - 主題版主
6. **subtopic_moderators** - 子主題版主

### 權限檢查函數

- `check_user_permission(user_id, permission_key)` - 檢查用戶權限
- `check_topic_moderator(user_id, topic_id)` - 檢查主題版主
- `check_subtopic_moderator(user_id, subtopic_id)` - 檢查子主題版主
- `check_content_management_permission(user_id, content_type, content_id)` - 檢查內容管理權限

## 使用方法

### 1. 安裝權限系統

```bash
# 在 Supabase Console 中執行 SQL 腳本
psql -f migration/role.sql
```

### 2. 在 API 中使用權限檢查

```typescript
import {
  requireAdmin,
  authenticateUser,
  requireAuthorOrModerator,
} from "@/lib/auth-helpers";

// 需要管理員權限
export async function GET() {
  const authResult = await requireAdmin();
  if (!authResult.success) {
    return authResult.response!;
  }

  const user = authResult.user;
  // 處理請求...
}

// 基本認證檢查（配合現有的 safeGetUser 模式）
export async function POST() {
  const authResult = await authenticateUser();
  if (!authResult.success) {
    return authResult.response!;
  }

  const user = authResult.user;
  // 處理請求...
}
```

### 3. 檢查內容管理權限

```typescript
import { requireAuthorOrModerator } from "@/lib/auth-helpers";

export async function PATCH(request: NextRequest) {
  const { contentType, contentId } = await request.json();

  const authResult = await requireAuthorOrModerator(contentType, contentId);
  if (!authResult.success) {
    return authResult.response!;
  }

  const user = authResult.user;
  const isAuthor = authResult.isAuthor;
  const isModerator = authResult.isModerator;
  // 用戶有權限管理此內容
}
```

### 4. 使用權限服務

```typescript
import { checkUserPermission, getUserRoles } from "@/lib/permission-service";

// 檢查用戶權限
const result = await checkUserPermission(userId, "manage_all_content");
if (result.hasPermission) {
  // 用戶有權限
}

// 獲取用戶角色
const rolesResult = await getUserRoles(userId);
console.log(rolesResult.roles);
```

## API 端點

### 角色管理

- `GET /api/admin/roles` - 獲取角色列表
- `POST /api/admin/roles` - 指派角色
- `DELETE /api/admin/roles` - 移除角色

### 用戶角色管理

- `GET /api/admin/users/[userId]/roles` - 獲取用戶角色
- `POST /api/admin/users/[userId]/roles` - 指派用戶角色
- `DELETE /api/admin/users/[userId]/roles` - 移除用戶角色

### 主題版主管理

- `GET /api/admin/topics/[topicId]/moderators` - 獲取主題版主
- `POST /api/admin/topics/[topicId]/moderators` - 指派主題版主
- `DELETE /api/admin/topics/[topicId]/moderators` - 移除主題版主

### 內容管理

- `GET /api/admin/content` - 獲取待審核內容
- `PATCH /api/admin/content` - 更新內容狀態

## 測試

### 運行權限系統測試

```bash
# 設置環境變數
export NEXT_PUBLIC_SUPABASE_URL="your_supabase_url"
export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# 運行測試腳本
node scripts/test-permissions.js
```

### 測試內容包括

- 角色表查詢
- 權限表查詢
- 角色權限關聯
- 權限檢查函數
- 數據庫索引

## 管理面板

訪問 `/admin/roles` 查看角色管理面板，包括：

- 系統角色列表
- 權限說明
- 快速操作連結

## 安全注意事項

1. **權限檢查** - 所有敏感操作都必須進行權限檢查
2. **過期處理** - 定期清理過期的角色指派
3. **審計日誌** - 記錄所有權限變更操作
4. **最小權限原則** - 只給予必要的最小權限
5. **SECURITY DEFINER** - 所有權限函數都使用 `SECURITY DEFINER` 並設定 `search_path = public`
6. **RLS 保護** - 所有角色相關表格都啟用了 Row Level Security
7. **函數穩定性** - 查詢函數標記為 `STABLE` 以提升性能

## 常見問題

### Q: 如何指派第一個站長？

A: 直接在數據庫中插入記錄到 `profile_roles` 表：

```sql
INSERT INTO profile_roles (profile_id, role_key, granted_by)
VALUES ('user_id_here', 'owner', 'user_id_here');
```

### Q: 如何清理過期角色？

A: 使用內建函數：

```sql
SELECT cleanup_expired_roles();
-- 返回 JSON 格式的清理結果：
-- {"profile_roles": 2, "topic_moderators": 1, "subtopic_moderators": 0, "total": 3}
```

### Q: 如何設定定期清理任務？

A: 可以使用 pg_cron 擴展：

```sql
-- 每天凌晨 2 點執行清理
SELECT cron.schedule('cleanup-expired-roles', '0 2 * * *', 'SELECT cleanup_expired_roles();');
```

### Q: 如何查看用戶的所有權限？

A: 使用權限服務：

```typescript
const roles = await getUserRoles(userId);
const topics = await getUserManagedTopics(userId);
const subtopics = await getUserManagedSubtopics(userId);
```

## 更新日誌

- **v1.0.0** - 初始版本，支援基本角色權限系統
- 支援站長、主題版主、子主題版主角色
- 完整的權限檢查和管理功能
- API 端點和管理面板
