import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

// 權限類型定義
export type Permission = 
  | 'manage_all_content'
  | 'manage_topic_content'
  | 'manage_subtopic_content'
  | 'approve_content'
  | 'reject_content'
  | 'delete_content'
  | 'edit_others_content'
  | 'manage_moderators'
  | 'manage_users'
  | 'ban_users'
  | 'manage_topics'
  | 'manage_subtopics'
  | 'view_admin_panel'
  | 'manage_system_settings'

export type Role = 'owner' | 'topic_mod' | 'subtopic_mod'

// 角色信息接口
export interface UserRole {
  role_key: Role
  role_name: string
  granted_at: string
  expires_at?: string
}

// 管理的主題/子主題接口
export interface ManagedTopic {
  topic_id: string
  topic_name: string
  topic_slug: string
}

export interface ManagedSubtopic {
  subtopic_id: string
  subtopic_name: string
  subtopic_slug: string
  topic_id: string
  topic_name: string
}

// 權限檢查結果
export interface PermissionResult {
  success: boolean
  hasPermission: boolean
  error?: string
}

// 獲取 Supabase 客戶端
async function getSupabaseClient() {
  const cookieStore = cookies()
  return await createServerClient(cookieStore)
}

/**
 * 檢查用戶是否有特定權限
 */
export async function checkUserPermission(
  userId: string,
  permission: Permission
): Promise<PermissionResult> {
  try {
    const supabase = await getSupabaseClient()
    
    const { data, error } = await supabase
      .rpc('check_user_permission', {
        user_id: userId,
        permission_key: permission
      })
    
    if (error) {
      console.error('檢查用戶權限時出錯:', error)
      return { success: false, hasPermission: false, error: error.message }
    }
    
    return { success: true, hasPermission: data || false }
  } catch (error: any) {
    console.error('檢查用戶權限時出錯:', error)
    return { success: false, hasPermission: false, error: error.message }
  }
}

/**
 * 檢查用戶是否為主題版主
 */
export async function checkTopicModerator(
  userId: string,
  topicId: string
): Promise<PermissionResult> {
  try {
    const supabase = await getSupabaseClient()
    
    const { data, error } = await supabase
      .rpc('check_topic_moderator', {
        user_id: userId,
        topic_id: topicId
      })
    
    if (error) {
      console.error('檢查主題版主權限時出錯:', error)
      return { success: false, hasPermission: false, error: error.message }
    }
    
    return { success: true, hasPermission: data || false }
  } catch (error: any) {
    console.error('檢查主題版主權限時出錯:', error)
    return { success: false, hasPermission: false, error: error.message }
  }
}

/**
 * 檢查用戶是否為子主題版主
 */
export async function checkSubtopicModerator(
  userId: string,
  subtopicId: string
): Promise<PermissionResult> {
  try {
    const supabase = await getSupabaseClient()
    
    const { data, error } = await supabase
      .rpc('check_subtopic_moderator', {
        user_id: userId,
        subtopic_id: subtopicId
      })
    
    if (error) {
      console.error('檢查子主題版主權限時出錯:', error)
      return { success: false, hasPermission: false, error: error.message }
    }
    
    return { success: true, hasPermission: data || false }
  } catch (error: any) {
    console.error('檢查子主題版主權限時出錯:', error)
    return { success: false, hasPermission: false, error: error.message }
  }
}

/**
 * 檢查用戶是否可以管理特定內容
 */
export async function checkContentManagementPermission(
  userId: string,
  contentType: 'card' | 'thread',
  contentId: string
): Promise<PermissionResult> {
  try {
    const supabase = await getSupabaseClient()

    // 1. 首先檢查是否為站長
    const ownerCheck = await checkUserPermission(userId, 'manage_all_content')
    if (ownerCheck.hasPermission) {
      return { success: true, hasPermission: true }
    }

    // 2. 獲取內容的主題和子主題信息
    let contentQuery
    if (contentType === 'card') {
      contentQuery = supabase
        .from('cards')
        .select(`
          id,
          card_topics (topic_id),
          card_subtopics (subtopic_id)
        `)
        .eq('id', contentId)
        .single()
    } else {
      contentQuery = supabase
        .from('threads')
        .select(`
          id,
          thread_topics (topic_id),
          thread_subtopics (subtopic_id)
        `)
        .eq('id', contentId)
        .single()
    }

    const { data: content, error: contentError } = await contentQuery
    if (contentError || !content) {
      return { success: false, hasPermission: false, error: '內容不存在' }
    }

    // 3. 檢查主題版主權限
    const topicIds = contentType === 'card'
      ? (content as any).card_topics?.map((ct: any) => ct.topic_id) || []
      : (content as any).thread_topics?.map((tt: any) => tt.topic_id) || []

    for (const topicId of topicIds) {
      const topicModeratorCheck = await checkTopicModerator(userId, topicId)
      if (topicModeratorCheck.hasPermission) {
        return { success: true, hasPermission: true }
      }
    }

    // 4. 檢查子主題版主權限
    const subtopicIds = contentType === 'card'
      ? (content as any).card_subtopics?.map((cs: any) => cs.subtopic_id) || []
      : (content as any).thread_subtopics?.map((ts: any) => ts.subtopic_id) || []

    for (const subtopicId of subtopicIds) {
      const subtopicModeratorCheck = await checkSubtopicModerator(userId, subtopicId)
      if (subtopicModeratorCheck.hasPermission) {
        return { success: true, hasPermission: true }
      }
    }

    // 5. 沒有權限
    return { success: true, hasPermission: false }

  } catch (error: any) {
    console.error('檢查內容管理權限時出錯:', error)
    return { success: false, hasPermission: false, error: error.message }
  }
}

/**
 * 獲取用戶的所有有效角色
 */
export async function getUserRoles(userId: string): Promise<{
  success: boolean
  roles: UserRole[]
  error?: string
}> {
  try {
    const supabase = await getSupabaseClient()
    
    const { data, error } = await supabase
      .rpc('get_user_roles', { user_id: userId })
    
    if (error) {
      console.error('獲取用戶角色時出錯:', error)
      return { success: false, roles: [], error: error.message }
    }
    
    return { success: true, roles: data || [] }
  } catch (error: any) {
    console.error('獲取用戶角色時出錯:', error)
    return { success: false, roles: [], error: error.message }
  }
}

/**
 * 獲取用戶管理的主題
 */
export async function getUserManagedTopics(userId: string): Promise<{
  success: boolean
  topics: ManagedTopic[]
  error?: string
}> {
  try {
    const supabase = await getSupabaseClient()
    
    const { data, error } = await supabase
      .rpc('get_user_managed_topics', { user_id: userId })
    
    if (error) {
      console.error('獲取用戶管理的主題時出錯:', error)
      return { success: false, topics: [], error: error.message }
    }
    
    return { success: true, topics: data || [] }
  } catch (error: any) {
    console.error('獲取用戶管理的主題時出錯:', error)
    return { success: false, topics: [], error: error.message }
  }
}

/**
 * 獲取用戶管理的子主題
 */
export async function getUserManagedSubtopics(userId: string): Promise<{
  success: boolean
  subtopics: ManagedSubtopic[]
  error?: string
}> {
  try {
    const supabase = await getSupabaseClient()
    
    const { data, error } = await supabase
      .rpc('get_user_managed_subtopics', { user_id: userId })
    
    if (error) {
      console.error('獲取用戶管理的子主題時出錯:', error)
      return { success: false, subtopics: [], error: error.message }
    }
    
    return { success: true, subtopics: data || [] }
  } catch (error: any) {
    console.error('獲取用戶管理的子主題時出錯:', error)
    return { success: false, subtopics: [], error: error.message }
  }
}

/**
 * 檢查用戶是否為管理員（站長或版主）
 */
export async function isUserModerator(userId: string): Promise<PermissionResult> {
  const adminCheck = await checkUserPermission(userId, 'view_admin_panel')
  return adminCheck
}

/**
 * 檢查用戶是否為站長
 */
export async function isUserOwner(userId: string): Promise<PermissionResult> {
  const ownerCheck = await checkUserPermission(userId, 'manage_all_content')
  return ownerCheck
}
