import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkUserPermission, checkTopicModerator } from "@/lib/permission-service"

/**
 * @swagger
 * /api/admin/topics/{topicId}/moderators:
 *   get:
 *     summary: 獲取主題的版主列表
 *     tags: [Admin - Topics]
 *     parameters:
 *       - in: path
 *         name: topicId
 *         required: true
 *         schema:
 *           type: string
 *         description: 主題ID
 *     responses:
 *       200:
 *         description: 成功獲取主題版主列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     topic:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         slug:
 *                           type: string
 *                     moderators:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           profile_id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           email:
 *                             type: string
 *                           granted_at:
 *                             type: string
 *                           expires_at:
 *                             type: string
 *                           granted_by_name:
 *                             type: string
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 主題不存在
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { topicId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { topicId } = params

    // 檢查用戶身份驗證
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: authError, isAuthError } = await safeGetUser(supabase)
    
    if (authError || isAuthError || !user) {
      return NextResponse.json(
        { success: false, error: "未授權訪問" },
        { status: 401 }
      )
    }

    // 檢查管理員權限或主題版主權限
    const adminCheck = await checkUserPermission(user.id, 'view_admin_panel')
    const topicModCheck = await checkTopicModerator(user.id, topicId)
    
    if ((!adminCheck.success || !adminCheck.hasPermission) && 
        (!topicModCheck.success || !topicModCheck.hasPermission)) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // 獲取主題基本信息
    const { data: topic, error: topicError } = await supabase
      .from('topics')
      .select('id, name, slug, description')
      .eq('id', topicId)
      .single()

    if (topicError || !topic) {
      return NextResponse.json(
        { success: false, error: "主題不存在" },
        { status: 404 }
      )
    }

    // 獲取主題版主列表
    const { data: moderators, error: moderatorsError } = await supabase
      .from('topic_moderators')
      .select(`
        profile_id,
        granted_at,
        expires_at,
        is_active,
        profiles:profile_id (
          id,
          name,
          email,
          avatar
        ),
        granted_by_profile:granted_by (
          name
        )
      `)
      .eq('topic_id', topicId)
      .eq('is_active', true)
      .order('granted_at', { ascending: false })

    if (moderatorsError) {
      console.error('獲取主題版主列表時出錯:', moderatorsError)
      return NextResponse.json(
        { success: false, error: "獲取版主列表失敗" },
        { status: 500 }
      )
    }

    // 格式化版主數據
    const formattedModerators = moderators?.map(mod => ({
      profile_id: mod.profile_id,
      name: mod.profiles?.name,
      email: mod.profiles?.email,
      avatar: mod.profiles?.avatar,
      granted_at: mod.granted_at,
      expires_at: mod.expires_at,
      granted_by_name: mod.granted_by_profile?.name
    })) || []

    return NextResponse.json({
      success: true,
      data: {
        topic,
        moderators: formattedModerators
      }
    })

  } catch (error: any) {
    console.error('處理獲取主題版主請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/topics/{topicId}/moderators:
 *   post:
 *     summary: 指派主題版主
 *     tags: [Admin - Topics]
 *     parameters:
 *       - in: path
 *         name: topicId
 *         required: true
 *         schema:
 *           type: string
 *         description: 主題ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: 用戶ID
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *                 description: 過期時間（可選）
 *     responses:
 *       200:
 *         description: 成功指派主題版主
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 主題或用戶不存在
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { topicId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { topicId } = params

    // 檢查用戶身份驗證
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: authError, isAuthError } = await safeGetUser(supabase)
    
    if (authError || isAuthError || !user) {
      return NextResponse.json(
        { success: false, error: "未授權訪問" },
        { status: 401 }
      )
    }

    // 檢查管理員權限
    const permissionCheck = await checkUserPermission(user.id, 'manage_moderators')
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // 解析請求體
    const body = await request.json()
    const { userId, expiresAt } = body

    // 驗證必要參數
    if (!userId) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：userId" },
        { status: 400 }
      )
    }

    // 驗證主題是否存在
    const { data: topic, error: topicError } = await supabase
      .from('topics')
      .select('id')
      .eq('id', topicId)
      .single()

    if (topicError || !topic) {
      return NextResponse.json(
        { success: false, error: "主題不存在" },
        { status: 404 }
      )
    }

    // 驗證目標用戶是否存在
    const { data: targetUser, error: targetUserError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single()

    if (targetUserError || !targetUser) {
      return NextResponse.json(
        { success: false, error: "目標用戶不存在" },
        { status: 404 }
      )
    }

    // 使用數據庫函數指派主題版主
    const { data, error } = await supabase
      .rpc('assign_topic_moderator', {
        topic_id: topicId,
        user_id: userId,
        granted_by_user_id: user.id,
        expires_at: expiresAt || null
      })

    if (error) {
      console.error('指派主題版主時出錯:', error)
      return NextResponse.json(
        { success: false, error: error.message || "指派主題版主失敗" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "主題版主指派成功"
    })

  } catch (error: any) {
    console.error('處理指派主題版主請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/topics/{topicId}/moderators:
 *   delete:
 *     summary: 移除主題版主
 *     tags: [Admin - Topics]
 *     parameters:
 *       - in: path
 *         name: topicId
 *         required: true
 *         schema:
 *           type: string
 *         description: 主題ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: 用戶ID
 *     responses:
 *       200:
 *         description: 成功移除主題版主
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { topicId: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { topicId } = params

    // 檢查用戶身份驗證
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: authError, isAuthError } = await safeGetUser(supabase)
    
    if (authError || isAuthError || !user) {
      return NextResponse.json(
        { success: false, error: "未授權訪問" },
        { status: 401 }
      )
    }

    // 檢查管理員權限
    const permissionCheck = await checkUserPermission(user.id, 'manage_moderators')
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // 解析請求體
    const body = await request.json()
    const { userId } = body

    // 驗證必要參數
    if (!userId) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：userId" },
        { status: 400 }
      )
    }

    // 移除主題版主
    const { error } = await supabase
      .from('topic_moderators')
      .update({ is_active: false })
      .eq('topic_id', topicId)
      .eq('profile_id', userId)

    if (error) {
      console.error('移除主題版主時出錯:', error)
      return NextResponse.json(
        { success: false, error: "移除主題版主失敗" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "主題版主移除成功"
    })

  } catch (error: any) {
    console.error('處理移除主題版主請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}
