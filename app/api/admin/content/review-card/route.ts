import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkContentManagementPermission } from "@/lib/permission-service"

/**
 * @swagger
 * /api/admin/content/review-card:
 *   post:
 *     summary: 審核卡片
 *     description: 批准或拒絕待審核的卡片
 *     tags:
 *       - Admin Content
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - cardId
 *               - action
 *             properties:
 *               cardId:
 *                 type: string
 *                 description: 卡片 ID
 *               action:
 *                 type: string
 *                 enum: [approve, reject]
 *                 description: 審核操作
 *               note:
 *                 type: string
 *                 description: 審核備註（可選）
 *     responses:
 *       200:
 *         description: 審核成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 卡片不存在
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶是否已登入
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // 解析請求體
    const body = await request.json()
    const { cardId, action, note } = body

    // 驗證必要參數
    if (!cardId || !action) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：cardId 和 action" },
        { status: 400 }
      )
    }

    if (!['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { success: false, error: "無效的操作類型，必須是 approve 或 reject" },
        { status: 400 }
      )
    }

    // 檢查卡片是否存在且為待審核狀態
    const { data: card, error: cardError } = await supabase
      .from('cards')
      .select(`
        id,
        title,
        status,
        author_id,
        card_topics (topic_id),
        card_subtopics (subtopic_id)
      `)
      .eq('id', cardId)
      .single()

    if (cardError || !card) {
      return NextResponse.json(
        { success: false, error: "卡片不存在" },
        { status: 404 }
      )
    }

    if (card.status !== 'pending') {
      return NextResponse.json(
        { success: false, error: "卡片不是待審核狀態" },
        { status: 400 }
      )
    }

    // 檢查用戶是否有權限審核此卡片
    const permissionCheck = await checkContentManagementPermission(user.id, 'card', cardId)
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足，無法審核此卡片" },
        { status: 403 }
      )
    }

    // 確定新的狀態
    const newStatus = action === 'approve' ? 'published' : 'rejected'
    const publishedAt = action === 'approve' ? new Date().toISOString() : null

    // 更新卡片狀態
    const { data: updatedCard, error: updateError } = await supabase
      .from('cards')
      .update({
        status: newStatus,
        published_at: publishedAt,
        updated_at: new Date().toISOString()
      })
      .eq('id', cardId)
      .select()
      .single()

    if (updateError) {
      console.error('更新卡片狀態時出錯:', updateError)
      return NextResponse.json(
        { success: false, error: "更新卡片狀態失敗" },
        { status: 500 }
      )
    }

    // 創建通知給作者（忽略錯誤）
    try {
      const notificationMessage = action === 'approve'
        ? `您的觀點卡「${card.title}」已通過審核並發布`
        : `您的觀點卡「${card.title}」未通過審核${note ? `，原因：${note}` : ''}`

      await supabase
        .from('notifications')
        .insert({
          profile_id: card.author_id,
          notif_type: action === 'approve' ? 'content_approved' : 'content_rejected',
          message: notificationMessage,
          ref_type: 'card',
          ref_id: cardId,
          is_read: false,
          created_at: new Date().toISOString()
        })
    } catch (notificationError) {
      console.warn('創建通知失敗:', notificationError)
    }

    return NextResponse.json({
      success: true,
      message: `卡片已${action === 'approve' ? '批准' : '拒絕'}`,
      data: {
        cardId,
        newStatus,
        publishedAt,
        reviewedBy: user.id,
        reviewedAt: new Date().toISOString(),
        note
      }
    })

  } catch (error: any) {
    console.error('審核卡片時出錯:', error)
    return NextResponse.json(
      { success: false, error: error.message || "服務器內部錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/content/review-card:
 *   patch:
 *     summary: 批量審核卡片
 *     description: 批量批准或拒絕多個待審核的卡片
 *     tags:
 *       - Admin Content
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - cardIds
 *               - action
 *             properties:
 *               cardIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 卡片 ID 列表
 *               action:
 *                 type: string
 *                 enum: [approve, reject]
 *                 description: 審核操作
 *               note:
 *                 type: string
 *                 description: 審核備註（可選）
 *     responses:
 *       200:
 *         description: 批量審核成功
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 */
export async function PATCH(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶是否已登入
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // 解析請求體
    const body = await request.json()
    const { cardIds, action, note } = body

    // 驗證必要參數
    if (!cardIds || !Array.isArray(cardIds) || cardIds.length === 0) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：cardIds 必須是非空數組" },
        { status: 400 }
      )
    }

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { success: false, error: "無效的操作類型，必須是 approve 或 reject" },
        { status: 400 }
      )
    }

    // 批量檢查權限和處理
    const results = []
    const errors = []

    for (const cardId of cardIds) {
      try {
        // 檢查權限
        const permissionCheck = await checkContentManagementPermission(user.id, 'card', cardId)
        if (!permissionCheck.success || !permissionCheck.hasPermission) {
          errors.push({ cardId, error: "權限不足" })
          continue
        }

        // 檢查卡片狀態
        const { data: card, error: cardError } = await supabase
          .from('cards')
          .select('id, status, title, author_id')
          .eq('id', cardId)
          .single()

        if (cardError || !card) {
          errors.push({ cardId, error: "卡片不存在" })
          continue
        }

        if (card.status !== 'pending') {
          errors.push({ cardId, error: "卡片不是待審核狀態" })
          continue
        }

        // 更新卡片狀態
        const newStatus = action === 'approve' ? 'published' : 'rejected'
        const publishedAt = action === 'approve' ? new Date().toISOString() : null

        const { error: updateError } = await supabase
          .from('cards')
          .update({
            status: newStatus,
            published_at: publishedAt,
            updated_at: new Date().toISOString()
          })
          .eq('id', cardId)

        if (updateError) {
          errors.push({ cardId, error: "更新失敗" })
          continue
        }

        results.push({ cardId, status: newStatus })

        // 創建通知（忽略錯誤）
        try {
          await supabase
            .from('notifications')
            .insert({
              profile_id: card.author_id,
              notif_type: action === 'approve' ? 'content_approved' : 'content_rejected',
              message: `您的觀點卡「${card.title}」已${action === 'approve' ? '通過審核並發布' : '被拒絕'}${note ? `，原因：${note}` : ''}`,
              ref_type: 'card',
              ref_id: cardId,
              is_read: false,
              created_at: new Date().toISOString()
            })
        } catch (notificationError) {
          console.warn('創建通知失敗:', notificationError)
        }

      } catch (error) {
        errors.push({ cardId, error: "處理失敗" })
      }
    }

    return NextResponse.json({
      success: true,
      message: `批量審核完成，成功處理 ${results.length} 個卡片`,
      data: {
        successful: results,
        failed: errors,
        total: cardIds.length,
        successCount: results.length,
        errorCount: errors.length
      }
    })

  } catch (error: any) {
    console.error('批量審核卡片時出錯:', error)
    return NextResponse.json(
      { success: false, error: error.message || "服務器內部錯誤" },
      { status: 500 }
    )
  }
}
