import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkUserPermission, checkContentManagementPermission } from "@/lib/permission-service"
import { requireAdmin } from "@/lib/auth-helpers"

/**
 * @swagger
 * /api/admin/content:
 *   get:
 *     summary: 獲取待審核的內容列表
 *     tags: [Admin - Content]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [card, thread, all]
 *         description: 內容類型
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, published, rejected, draft]
 *         description: 內容狀態
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每頁數量
 *     responses:
 *       200:
 *         description: 成功獲取內容列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     cards:
 *                       type: array
 *                     threads:
 *                       type: array
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 */
export async function GET(request: NextRequest) {
  try {
    // 使用輔助函數進行認證和權限檢查
    const authResult = await requireAdmin()
    if (!authResult.success) {
      return authResult.response!
    }

    const user = authResult.user
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { searchParams } = new URL(request.url)

    const type = searchParams.get('type') || 'all'
    const status = searchParams.get('status') || 'pending'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let cards: any[] = []
    let threads: any[] = []
    let totalCards = 0
    let totalThreads = 0

    // 檢查是否為站長（可以看到所有內容）
    const isOwner = await checkUserPermission(user.id, 'manage_all_content')

    // 獲取卡片數據
    if (type === 'card' || type === 'all') {
      let cardsQuery = supabase
        .from('cards')
        .select(`
          id, title, content, status, semantic_type, contribution_type,
          created_at, updated_at, published_at,
          author:author_id (id, name, email, avatar),
          topics:card_topics (topic:topic_id (id, name, slug)),
          subtopics:card_subtopics (subtopic:subtopic_id (id, name, slug))
        `, { count: 'exact' })
        .eq('status', status)
        .order('created_at', { ascending: false })

      // 如果不是站長，只顯示用戶有權限管理的內容
      if (!isOwner.hasPermission) {
        // 這裡需要根據用戶的版主權限過濾內容
        // 暫時先顯示所有內容，後續可以優化
      }

      const { data: cardsData, error: cardsError, count: cardsCount } = await cardsQuery
        .range(offset, offset + limit - 1)

      if (cardsError) {
        console.error('獲取卡片數據時出錯:', cardsError)
      } else {
        cards = cardsData || []
        totalCards = cardsCount || 0
      }
    }

    // 獲取討論串數據
    if (type === 'thread' || type === 'all') {
      let threadsQuery = supabase
        .from('threads')
        .select(`
          id, title, content, status, semantic_type,
          created_at, updated_at, published_at,
          author:author_id (id, name, email, avatar),
          topics:thread_topics (topic:topic_id (id, name, slug)),
          subtopics:thread_subtopics (subtopic:subtopic_id (id, name, slug))
        `, { count: 'exact' })
        .eq('status', status)
        .order('created_at', { ascending: false })

      // 如果不是站長，只顯示用戶有權限管理的內容
      if (!isOwner.hasPermission) {
        // 這裡需要根據用戶的版主權限過濾內容
        // 暫時先顯示所有內容，後續可以優化
      }

      const { data: threadsData, error: threadsError, count: threadsCount } = await threadsQuery
        .range(offset, offset + limit - 1)

      if (threadsError) {
        console.error('獲取討論串數據時出錯:', threadsError)
      } else {
        threads = threadsData || []
        totalThreads = threadsCount || 0
      }
    }

    const total = type === 'all' ? totalCards + totalThreads : 
                  type === 'card' ? totalCards : totalThreads
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: {
        cards,
        threads,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    })

  } catch (error: any) {
    console.error('處理獲取內容列表請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/content:
 *   patch:
 *     summary: 更新內容狀態（審核/拒絕）
 *     tags: [Admin - Content]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contentType
 *               - contentId
 *               - status
 *             properties:
 *               contentType:
 *                 type: string
 *                 enum: [card, thread]
 *                 description: 內容類型
 *               contentId:
 *                 type: string
 *                 description: 內容ID
 *               status:
 *                 type: string
 *                 enum: [published, rejected, pending]
 *                 description: 新狀態
 *               reason:
 *                 type: string
 *                 description: 拒絕原因（當狀態為rejected時）
 *     responses:
 *       200:
 *         description: 成功更新內容狀態
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 *       404:
 *         description: 內容不存在
 */
export async function PATCH(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶身份驗證
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: authError, isAuthError } = await safeGetUser(supabase)
    
    if (authError || isAuthError || !user) {
      return NextResponse.json(
        { success: false, error: "未授權訪問" },
        { status: 401 }
      )
    }

    // 解析請求體
    const body = await request.json()
    const { contentType, contentId, status, reason } = body

    // 驗證必要參數
    if (!contentType || !contentId || !status) {
      return NextResponse.json(
        { success: false, error: "缺少必要參數：contentType、contentId 和 status" },
        { status: 400 }
      )
    }

    // 驗證內容類型
    if (!['card', 'thread'].includes(contentType)) {
      return NextResponse.json(
        { success: false, error: "無效的內容類型" },
        { status: 400 }
      )
    }

    // 驗證狀態
    if (!['published', 'rejected', 'pending'].includes(status)) {
      return NextResponse.json(
        { success: false, error: "無效的狀態" },
        { status: 400 }
      )
    }

    // 檢查用戶是否有權限管理此內容
    const contentPermissionCheck = await checkContentManagementPermission(user.id, contentType, contentId)
    if (!contentPermissionCheck.success || !contentPermissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足，無法管理此內容" },
        { status: 403 }
      )
    }

    // 更新內容狀態
    const tableName = contentType === 'card' ? 'cards' : 'threads'
    const updateData: any = { 
      status,
      updated_at: new Date().toISOString()
    }

    // 如果是發布狀態，設置發布時間
    if (status === 'published') {
      updateData.published_at = new Date().toISOString()
    }

    const { data, error } = await supabase
      .from(tableName)
      .update(updateData)
      .eq('id', contentId)
      .select()
      .single()

    if (error) {
      console.error('更新內容狀態時出錯:', error)
      return NextResponse.json(
        { success: false, error: "更新內容狀態失敗" },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { success: false, error: "內容不存在" },
        { status: 404 }
      )
    }

    // TODO: 如果需要，可以在這裡發送通知給內容作者

    return NextResponse.json({
      success: true,
      message: `內容狀態已更新為 ${status}`,
      data
    })

  } catch (error: any) {
    console.error('處理更新內容狀態請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤" },
      { status: 500 }
    )
  }
}
