import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import { checkUserPermission, getUserManagedTopics, getUserManagedSubtopics } from "@/lib/permission-service"
import { formatDistanceToNow } from "date-fns"
import { zhTW } from "date-fns/locale"

/**
 * @swagger
 * /api/admin/content/pending-cards:
 *   get:
 *     summary: 獲取待審核卡片列表
 *     description: 根據用戶權限返回相應範圍的待審核卡片
 *     tags:
 *       - Admin Content
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每頁數量
 *       - in: query
 *         name: topic_id
 *         schema:
 *           type: string
 *         description: 篩選特定主題
 *       - in: query
 *         name: subtopic_id
 *         schema:
 *           type: string
 *         description: 篩選特定子主題
 *     responses:
 *       200:
 *         description: 成功獲取待審核卡片列表
 *       401:
 *         description: 未授權
 *       403:
 *         description: 權限不足
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const { searchParams } = new URL(request.url)

    // 檢查用戶是否已登入
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: userError, isAuthError } = await safeGetUser(supabase)

    if (userError || isAuthError) {
      return NextResponse.json({ success: false, error: "認證失敗，請重新登入" }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // 檢查基本管理權限
    const permissionCheck = await checkUserPermission(user.id, 'view_admin_panel')
    if (!permissionCheck.success || !permissionCheck.hasPermission) {
      return NextResponse.json(
        { success: false, error: "權限不足" },
        { status: 403 }
      )
    }

    // 解析查詢參數
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const topicFilter = searchParams.get('topic_id')
    const subtopicFilter = searchParams.get('subtopic_id')
    const offset = (page - 1) * limit

    // 檢查用戶權限級別
    const isOwner = await checkUserPermission(user.id, 'manage_all_content')
    
    let query = supabase
      .from('cards')
      .select(`
        id,
        title,
        content,
        semantic_type,
        contribution_type,
        original_author,
        original_url,
        status,
        created_at,
        updated_at,
        profiles:author_id (
          id,
          name,
          avatar,
          email
        ),
        card_topics (
          topics (
            id,
            name,
            slug
          )
        ),
        card_subtopics (
          subtopics (
            id,
            name,
            slug,
            topic_id
          )
        )
      `, { count: 'exact' })
      .eq('status', 'pending')
      .order('created_at', { ascending: false })

    // 根據用戶權限過濾卡片
    if (isOwner.hasPermission) {
      // 站長可以看到所有待審核卡片
      console.log('User is owner, showing all pending cards')
    } else {
      // 獲取用戶管理的主題和子主題
      const [topicsResult, subtopicsResult] = await Promise.all([
        getUserManagedTopics(user.id),
        getUserManagedSubtopics(user.id)
      ])

      if (!topicsResult.success || !subtopicsResult.success) {
        return NextResponse.json(
          { success: false, error: "獲取權限信息失敗" },
          { status: 500 }
        )
      }

      const managedTopicIds = topicsResult.topics.map(t => t.topic_id)
      const managedSubtopicIds = subtopicsResult.subtopics.map(s => s.subtopic_id)

      if (managedTopicIds.length === 0 && managedSubtopicIds.length === 0) {
        // 用戶沒有管理任何主題或子主題
        return NextResponse.json({
          success: true,
          data: {
            cards: [],
            pagination: {
              page,
              limit,
              total: 0,
              totalPages: 0
            }
          }
        })
      }

      // 構建權限過濾條件
      // 需要使用複雜查詢來過濾用戶有權限的卡片
      // 這裡我們先獲取所有待審核卡片，然後在應用層過濾
      // 在生產環境中，建議使用數據庫函數來優化性能
    }

    // 應用篩選條件
    if (topicFilter) {
      // 這裡需要通過 card_topics 表來過濾
      // 暫時跳過，後續優化
    }

    if (subtopicFilter) {
      // 這裡需要通過 card_subtopics 表來過濾
      // 暫時跳過，後續優化
    }

    // 執行查詢
    const { data: cards, error, count } = await query
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('獲取待審核卡片時出錯:', error)
      return NextResponse.json(
        { success: false, error: "獲取待審核卡片失敗" },
        { status: 500 }
      )
    }

    // 如果不是站長，需要在應用層過濾權限
    let filteredCards = cards || []
    if (!isOwner.hasPermission) {
      const [topicsResult, subtopicsResult] = await Promise.all([
        getUserManagedTopics(user.id),
        getUserManagedSubtopics(user.id)
      ])

      const managedTopicIds = topicsResult.topics.map(t => t.topic_id)
      const managedSubtopicIds = subtopicsResult.subtopics.map(s => s.subtopic_id)

      filteredCards = cards?.filter(card => {
        // 檢查卡片是否屬於用戶管理的主題
        const cardTopicIds = card.card_topics?.map(ct => ct.topics.id) || []
        const cardSubtopicIds = card.card_subtopics?.map(cs => cs.subtopics.id) || []

        const hasTopicPermission = cardTopicIds.some(topicId => managedTopicIds.includes(topicId))
        const hasSubtopicPermission = cardSubtopicIds.some(subtopicId => managedSubtopicIds.includes(subtopicId))

        return hasTopicPermission || hasSubtopicPermission
      }) || []
    }

    // 格式化數據為 ContentCard 組件所需的格式
    const formattedCards = filteredCards.map(card => ({
      contentType: "viewpoint" as const,
      id: card.id,
      title: card.title,
      content: card.content,
      author: {
        id: card.profiles?.id || '',
        name: card.profiles?.name || '未知用戶',
        avatar: card.profiles?.avatar || '',
        email: card.profiles?.email || ''
      },
      topics: card.card_topics?.map(ct => ({
        id: ct.topics.id,
        name: ct.topics.name,
        slug: ct.topics.slug
      })) || [],
      subtopics: card.card_subtopics?.map(cs => ({
        id: cs.subtopics.id,
        name: cs.subtopics.name,
        slug: cs.subtopics.slug,
        topic_id: cs.subtopics.topic_id
      })) || [],
      semanticType: card.semantic_type,
      contribution_type: card.contribution_type,
      originalAuthor: card.original_author,
      originalSource: card.original_url,
      timestamp: formatDistanceToNow(new Date(card.created_at), { 
        addSuffix: true, 
        locale: zhTW 
      }),
      stats: {
        likes: 0,
        dislikes: 0,
        comments: 0,
        bookmarks: 0,
        views: 0
      },
      status: card.status,
      created_at: card.created_at,
      updated_at: card.updated_at
    }))

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      success: true,
      data: {
        cards: formattedCards,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
          hasMore: page < totalPages
        }
      }
    })

  } catch (error: any) {
    console.error('獲取待審核卡片時出錯:', error)
    return NextResponse.json(
      { success: false, error: error.message || "服務器內部錯誤" },
      { status: 500 }
    )
  }
}
