import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"
import {
  checkUserPermission,
  getUserRoles,
  getUserManagedTopics,
  getUserManagedSubtopics,
  isUserModerator,
  isUserOwner
} from "@/lib/permission-service"
import { authenticateUser } from "@/lib/auth-helpers"

/**
 * @swagger
 * /api/admin/test-permissions:
 *   get:
 *     summary: 測試權限系統
 *     tags: [Admin - Test]
 *     responses:
 *       200:
 *         description: 權限測試結果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                     permissions:
 *                       type: object
 *                     roles:
 *                       type: array
 *                     managedTopics:
 *                       type: array
 *                     managedSubtopics:
 *                       type: array
 *                     systemStatus:
 *                       type: object
 *       401:
 *         description: 未授權
 */
export async function GET(request: NextRequest) {
  try {
    // 使用輔助函數進行認證
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const user = authResult.user
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    const userId = user.id

    // 測試各種權限
    const permissionTests = [
      'view_admin_panel',
      'manage_all_content',
      'manage_moderators',
      'approve_content',
      'manage_topics',
      'manage_subtopics'
    ]

    const permissionResults: Record<string, boolean> = {}
    
    for (const permission of permissionTests) {
      const result = await checkUserPermission(userId, permission as any)
      permissionResults[permission] = result.hasPermission
    }

    // 獲取用戶角色
    const rolesResult = await getUserRoles(userId)
    
    // 獲取管理的主題
    const topicsResult = await getUserManagedTopics(userId)
    
    // 獲取管理的子主題
    const subtopicsResult = await getUserManagedSubtopics(userId)

    // 檢查用戶類型
    const isModeratorResult = await isUserModerator(userId)
    const isOwnerResult = await isUserOwner(userId)

    // 測試數據庫函數
    const { data: dbPermissionTest, error: dbError } = await supabase
      .rpc('check_user_permission', {
        user_id: userId,
        permission_key: 'view_admin_panel'
      })

    // 測試清理函數
    const { data: cleanupResult, error: cleanupError } = await supabase
      .rpc('cleanup_expired_roles')

    // 獲取系統統計
    const { data: rolesCount } = await supabase
      .from('roles')
      .select('*', { count: 'exact', head: true })

    const { data: permissionsCount } = await supabase
      .from('permissions')
      .select('*', { count: 'exact', head: true })

    const { data: activeRolesCount } = await supabase
      .from('profile_roles')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          isAuthenticated: true
        },
        permissions: {
          individual: permissionResults,
          isModerator: isModeratorResult.hasPermission,
          isOwner: isOwnerResult.hasPermission
        },
        roles: {
          success: rolesResult.success,
          data: rolesResult.roles,
          error: rolesResult.error
        },
        managedTopics: {
          success: topicsResult.success,
          data: topicsResult.topics,
          error: topicsResult.error
        },
        managedSubtopics: {
          success: subtopicsResult.success,
          data: subtopicsResult.subtopics,
          error: subtopicsResult.error
        },
        databaseTests: {
          permissionFunction: {
            success: !dbError,
            result: dbPermissionTest,
            error: dbError?.message
          },
          cleanupFunction: {
            success: !cleanupError,
            result: cleanupResult,
            error: cleanupError?.message
          }
        },
        systemStatus: {
          totalRoles: rolesCount?.length || 0,
          totalPermissions: permissionsCount?.length || 0,
          activeUserRoles: activeRolesCount?.length || 0,
          timestamp: new Date().toISOString()
        }
      }
    })

  } catch (error: any) {
    console.error('處理權限測試請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤", details: error.message },
      { status: 500 }
    )
  }
}

/**
 * @swagger
 * /api/admin/test-permissions:
 *   post:
 *     summary: 測試特定權限或內容管理權限
 *     tags: [Admin - Test]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               testType:
 *                 type: string
 *                 enum: [permission, content, topic, subtopic]
 *                 description: 測試類型
 *               permission:
 *                 type: string
 *                 description: 權限鍵值（當 testType 為 permission 時）
 *               contentType:
 *                 type: string
 *                 enum: [card, thread]
 *                 description: 內容類型（當 testType 為 content 時）
 *               contentId:
 *                 type: string
 *                 description: 內容ID（當 testType 為 content 時）
 *               topicId:
 *                 type: string
 *                 description: 主題ID（當 testType 為 topic 時）
 *               subtopicId:
 *                 type: string
 *                 description: 子主題ID（當 testType 為 subtopic 時）
 *     responses:
 *       200:
 *         description: 測試結果
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 */
export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    // 檢查用戶身份驗證
    const { safeGetUser } = await import("@/lib/api-utils")
    const { user, error: authError, isAuthError } = await safeGetUser(supabase)
    
    if (authError || isAuthError || !user) {
      return NextResponse.json(
        { success: false, error: "未授權訪問" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { testType, permission, contentType, contentId, topicId, subtopicId } = body

    if (!testType) {
      return NextResponse.json(
        { success: false, error: "缺少 testType 參數" },
        { status: 400 }
      )
    }

    let testResult: any = {}

    switch (testType) {
      case 'permission':
        if (!permission) {
          return NextResponse.json(
            { success: false, error: "缺少 permission 參數" },
            { status: 400 }
          )
        }
        const permResult = await checkUserPermission(user.id, permission)
        testResult = {
          type: 'permission',
          permission,
          hasPermission: permResult.hasPermission,
          success: permResult.success,
          error: permResult.error
        }
        break

      case 'content':
        if (!contentType || !contentId) {
          return NextResponse.json(
            { success: false, error: "缺少 contentType 或 contentId 參數" },
            { status: 400 }
          )
        }
        const { checkContentManagementPermission } = await import("@/lib/permission-service")
        const contentResult = await checkContentManagementPermission(user.id, contentType, contentId)
        testResult = {
          type: 'content',
          contentType,
          contentId,
          hasPermission: contentResult.hasPermission,
          success: contentResult.success,
          error: contentResult.error
        }
        break

      case 'topic':
        if (!topicId) {
          return NextResponse.json(
            { success: false, error: "缺少 topicId 參數" },
            { status: 400 }
          )
        }
        const { checkTopicModerator } = await import("@/lib/permission-service")
        const topicResult = await checkTopicModerator(user.id, topicId)
        testResult = {
          type: 'topic',
          topicId,
          hasPermission: topicResult.hasPermission,
          success: topicResult.success,
          error: topicResult.error
        }
        break

      case 'subtopic':
        if (!subtopicId) {
          return NextResponse.json(
            { success: false, error: "缺少 subtopicId 參數" },
            { status: 400 }
          )
        }
        const { checkSubtopicModerator } = await import("@/lib/permission-service")
        const subtopicResult = await checkSubtopicModerator(user.id, subtopicId)
        testResult = {
          type: 'subtopic',
          subtopicId,
          hasPermission: subtopicResult.hasPermission,
          success: subtopicResult.success,
          error: subtopicResult.error
        }
        break

      default:
        return NextResponse.json(
          { success: false, error: "無效的 testType" },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email
        },
        test: testResult,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    console.error('處理權限測試請求時出錯:', error)
    return NextResponse.json(
      { success: false, error: "內部伺服器錯誤", details: error.message },
      { status: 500 }
    )
  }
}
