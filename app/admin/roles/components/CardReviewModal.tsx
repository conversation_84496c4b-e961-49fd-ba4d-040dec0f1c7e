"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { CheckCircle, XCircle, Loader2, User, Calendar, Tag, ExternalLink } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { RichTextContent } from "@/components/rich-text-content"
import { getSemanticTypeConfig } from "@/lib/constants"
import { formatDistanceToNow } from "date-fns"
import { zhTW } from "date-fns/locale"
import { cn } from "@/lib/utils"

interface PendingCard {
  contentType: "viewpoint"
  id: string
  title: string
  content: string
  author: {
    id: string
    name: string
    avatar: string
    email: string
  }
  topics: Array<{
    id: string
    name: string
    slug: string
  }>
  subtopics: Array<{
    id: string
    name: string
    slug: string
    topic_id: string
  }>
  semanticType: string
  contribution_type?: string
  originalAuthor?: string
  originalSource?: string
  timestamp: string
  stats: {
    likes: number
    dislikes: number
    comments: number
    bookmarks: number
    views: number
  }
  status: string
  created_at: string
  updated_at: string
}

interface CardReviewModalProps {
  card: PendingCard | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onReviewed: () => void
}

export function CardReviewModal({ card, open, onOpenChange, onReviewed }: CardReviewModalProps) {
  const { toast } = useToast()
  const [reviewNote, setReviewNote] = useState("")
  const [isReviewing, setIsReviewing] = useState(false)

  const handleReview = async (action: 'approve' | 'reject') => {
    if (!card) return

    setIsReviewing(true)
    try {
      const response = await fetch('/api/admin/content/review-card', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          cardId: card.id,
          action,
          note: reviewNote.trim() || undefined
        })
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: `卡片已${action === 'approve' ? '通過' : '拒絕'}`,
          description: data.message
        })
        onReviewed()
        onOpenChange(false)
        setReviewNote("")
      } else {
        toast({
          title: "審核失敗",
          description: data.error || "請稍後再試",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('審核失敗:', error)
      toast({
        title: "審核失敗",
        description: "請稍後再試",
        variant: "destructive"
      })
    } finally {
      setIsReviewing(false)
    }
  }

  const handleClose = () => {
    if (!isReviewing) {
      onOpenChange(false)
      setReviewNote("")
    }
  }

  // 鍵盤快捷鍵
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && !isReviewing) {
      handleClose()
    }
    // Ctrl/Cmd + Enter 快速通過
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter' && !isReviewing) {
      handleReview('approve')
    }
  }

  if (!card) return null

  const semanticConfig = getSemanticTypeConfig(card.semanticType)

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent 
        className="max-w-4xl max-h-[90vh] overflow-y-auto"
        onKeyDown={handleKeyDown}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span>內容審核</span>
            <Badge variant="outline" className="text-orange-600 border-orange-200">
              待審核
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 卡片詳細內容 */}
          <Card className="overflow-hidden">
            <CardHeader className="pb-4">
              {/* 標籤區域 */}
              <div className="flex flex-wrap items-center gap-2 mb-4">
                {/* 語義類型標籤 */}
                <Badge
                  variant="outline"
                  className={cn(
                    "flex items-center gap-1",
                    semanticConfig.color
                  )}
                >
                  {semanticConfig.icon}
                  <span>{semanticConfig.label}</span>
                </Badge>

                {/* 貢獻類型標籤 */}
                {card.contribution_type && (
                  <Badge variant="secondary">
                    {card.contribution_type}
                  </Badge>
                )}

                {/* 主題標籤 */}
                {card.topics.map(topic => (
                  <Badge key={topic.id} variant="outline" className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    {topic.name}
                  </Badge>
                ))}

                {/* 子主題標籤 */}
                {card.subtopics.map(subtopic => (
                  <Badge key={subtopic.id} variant="secondary" className="text-xs">
                    {subtopic.name}
                  </Badge>
                ))}
              </div>

              {/* 標題 */}
              <h2 className="text-xl font-bold leading-tight">{card.title}</h2>

              {/* 作者和時間信息 */}
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={card.author.avatar} alt={card.author.name} />
                    <AvatarFallback>
                      <User className="h-3 w-3" />
                    </AvatarFallback>
                  </Avatar>
                  <span>{card.author.name}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{card.timestamp}</span>
                </div>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* 內容 */}
              <div className="prose prose-sm max-w-none mb-6">
                <RichTextContent content={card.content} />
              </div>

              {/* 原始來源 */}
              {card.originalSource && (
                <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border">
                  <ExternalLink className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium mb-1">原始出處</div>
                    <a
                      href={card.originalSource}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline truncate block"
                    >
                      {card.originalSource}
                    </a>
                  </div>
                </div>
              )}

              {/* 原始作者 */}
              {card.originalAuthor && (
                <div className="mt-4 text-sm text-muted-foreground">
                  <span className="font-medium">原始作者：</span>
                  {card.originalAuthor}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 審核操作區域 */}
          <div className="border-t pt-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="review-note">審核備註（可選）</Label>
              <Textarea
                id="review-note"
                value={reviewNote}
                onChange={(e) => setReviewNote(e.target.value)}
                placeholder="添加審核備註，如拒絕原因或修改建議..."
                className="min-h-[80px]"
                disabled={isReviewing}
              />
              <p className="text-xs text-muted-foreground">
                備註將會發送給作者作為通知
              </p>
            </div>
            
            <div className="flex gap-3 justify-end pt-4">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isReviewing}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleReview('reject')}
                disabled={isReviewing}
              >
                {isReviewing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <XCircle className="h-4 w-4 mr-2" />
                )}
                拒絕
              </Button>
              <Button
                onClick={() => handleReview('approve')}
                disabled={isReviewing}
                className="bg-green-600 hover:bg-green-700"
              >
                {isReviewing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                通過
              </Button>
            </div>

            {/* 快捷鍵提示 */}
            <div className="text-xs text-muted-foreground text-center pt-2 border-t">
              快捷鍵：Esc 關閉，Ctrl+Enter 快速通過
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
