"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, CheckCircle, XCircle, Users } from "lucide-react"

interface ReviewStatsProps {
  totalPending: number
  selectedCount: number
}

export function ReviewStats({ totalPending, selectedCount }: ReviewStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* 待審核總數 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">待審核卡片</CardTitle>
          <Clock className="h-4 w-4 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">{totalPending}</div>
          <p className="text-xs text-muted-foreground">
            需要審核的觀點卡
          </p>
        </CardContent>
      </Card>

      {/* 已選擇數量 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">已選擇</CardTitle>
          <Users className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{selectedCount}</div>
          <p className="text-xs text-muted-foreground">
            可進行批量操作
          </p>
        </CardContent>
      </Card>

      {/* 審核進度 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">審核進度</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {totalPending === 0 ? '100%' : '0%'}
          </div>
          <p className="text-xs text-muted-foreground">
            {totalPending === 0 ? '全部完成' : '等待處理'}
          </p>
        </CardContent>
      </Card>

      {/* 狀態摘要 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">狀態摘要</CardTitle>
          <XCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Badge variant="outline" className="text-orange-600 border-orange-200">
                待審核
              </Badge>
              <span className="text-sm font-medium">{totalPending}</span>
            </div>
            {selectedCount > 0 && (
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-blue-600 border-blue-200">
                  已選擇
                </Badge>
                <span className="text-sm font-medium">{selectedCount}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
