"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X, Filter, RotateCcw } from "lucide-react"
import { getSemanticTypeConfig } from "@/lib/constants"

interface Topic {
  id: string
  name: string
  slug: string
}

interface Subtopic {
  id: string
  name: string
  slug: string
  topic_id: string
}

interface Filters {
  topicId?: string
  subtopicId?: string
  semanticType?: string
  sortBy?: 'newest' | 'oldest'
}

interface ReviewFiltersProps {
  filters: Filters
  onFiltersChange: (filters: Filters) => void
}

export function ReviewFilters({ filters, onFiltersChange }: ReviewFiltersProps) {
  const [topics, setTopics] = useState<Topic[]>([])
  const [subtopics, setSubtopics] = useState<Subtopic[]>([])
  const [availableSubtopics, setAvailableSubtopics] = useState<Subtopic[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 語義類型選項
  const semanticTypes = [
    { value: 'insight', label: '💡 看法' },
    { value: 'experience', label: '🧪 實測經驗' },
    { value: 'guide', label: '🛠️ 工具教學' },
    { value: 'trap', label: '⚠️ 踩坑警示' },
    { value: 'debate', label: '🤔 爭議論點' },
    { value: 'concept', label: '📚 概念整理' }
  ]

  // 排序選項
  const sortOptions = [
    { value: 'newest', label: '最新提交' },
    { value: 'oldest', label: '最早提交' }
  ]

  // 載入主題和子主題
  useEffect(() => {
    const fetchTopicsAndSubtopics = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/topics')
        const data = await response.json()
        
        if (data.success) {
          setTopics(data.data)
          // 提取所有子主題
          const allSubtopics = data.data.flatMap((topic: any) => 
            topic.subtopics?.map((subtopic: any) => ({
              ...subtopic,
              topic_id: topic.id
            })) || []
          )
          setSubtopics(allSubtopics)
        }
      } catch (error) {
        console.error('載入主題和子主題失敗:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTopicsAndSubtopics()
  }, [])

  // 當選擇主題時，更新可用的子主題
  useEffect(() => {
    if (filters.topicId) {
      const filteredSubtopics = subtopics.filter(
        subtopic => subtopic.topic_id === filters.topicId
      )
      setAvailableSubtopics(filteredSubtopics)
      
      // 如果當前選中的子主題不屬於新選中的主題，清除子主題選擇
      if (filters.subtopicId && !filteredSubtopics.find(s => s.id === filters.subtopicId)) {
        onFiltersChange({ ...filters, subtopicId: undefined })
      }
    } else {
      setAvailableSubtopics(subtopics)
    }
  }, [filters.topicId, subtopics])

  // 處理篩選器變更
  const handleFilterChange = (key: keyof Filters, value: string | undefined) => {
    const newFilters = { ...filters, [key]: value }
    
    // 如果清除主題選擇，也清除子主題選擇
    if (key === 'topicId' && !value) {
      newFilters.subtopicId = undefined
    }
    
    onFiltersChange(newFilters)
  }

  // 清除所有篩選器
  const clearAllFilters = () => {
    onFiltersChange({})
  }

  // 清除單個篩選器
  const clearFilter = (key: keyof Filters) => {
    handleFilterChange(key, undefined)
  }

  // 獲取當前篩選器的顯示標籤
  const getFilterLabel = (key: keyof Filters, value: string) => {
    // 跳過 "all" 值，因為它代表沒有篩選
    if (value === "all") return ""

    switch (key) {
      case 'topicId':
        return topics.find(t => t.id === value)?.name || value
      case 'subtopicId':
        return subtopics.find(s => s.id === value)?.name || value
      case 'semanticType':
        return semanticTypes.find(s => s.value === value)?.label || value
      case 'sortBy':
        return sortOptions.find(s => s.value === value)?.label || value
      default:
        return value
    }
  }

  // 計算活躍篩選器數量
  const activeFiltersCount = Object.values(filters).filter(Boolean).length

  return (
    <div className="space-y-4">
      {/* 篩選器控制 */}
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">篩選條件</span>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </div>

        {/* 主題篩選 */}
        <Select
          value={filters.topicId || "all"}
          onValueChange={(value) => handleFilterChange('topicId', value === "all" ? undefined : value)}
          disabled={isLoading}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="選擇主題" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有主題</SelectItem>
            {topics.map(topic => (
              <SelectItem key={topic.id} value={topic.id}>
                {topic.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 子主題篩選 */}
        <Select
          value={filters.subtopicId || "all"}
          onValueChange={(value) => handleFilterChange('subtopicId', value === "all" ? undefined : value)}
          disabled={isLoading || availableSubtopics.length === 0}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="選擇子主題" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有子主題</SelectItem>
            {availableSubtopics.map(subtopic => (
              <SelectItem key={subtopic.id} value={subtopic.id}>
                {subtopic.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 語義類型篩選 */}
        <Select
          value={filters.semanticType || "all"}
          onValueChange={(value) => handleFilterChange('semanticType', value === "all" ? undefined : value)}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="選擇類型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有類型</SelectItem>
            {semanticTypes.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 排序 */}
        <Select
          value={filters.sortBy || "newest"}
          onValueChange={(value) => handleFilterChange('sortBy', value as 'newest' | 'oldest')}
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* 清除所有篩選器 */}
        {activeFiltersCount > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFilters}
            className="text-muted-foreground"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            清除篩選
          </Button>
        )}
      </div>

      {/* 活躍篩選器標籤 */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">當前篩選：</span>
          {Object.entries(filters).map(([key, value]) => {
            if (!value) return null
            const label = getFilterLabel(key as keyof Filters, value)
            if (!label) return null // 跳過空標籤（如 "all" 值）
            return (
              <Badge
                key={key}
                variant="secondary"
                className="flex items-center gap-1 pr-1"
              >
                <span>{label}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => clearFilter(key as keyof Filters)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )
          })}
        </div>
      )}
    </div>
  )
}
