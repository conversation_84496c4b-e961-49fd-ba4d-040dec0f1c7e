"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Shield, Users, Settings, FileText } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { ContentReview } from "./components/ContentReview"

interface Role {
  role_key: string
  name: string
  description: string
  level: number
}

export default function AdminRolesPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // 未登入自動重定向至登入頁（與 library 和 my-posts 行為一致）
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.replace('/auth/login')
    }
  }, [isLoading, isAuthenticated, router])

  // 如果正在檢查認證狀態，顯示加載畫面
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-muted-foreground">正在檢查認證狀態...</p>
        </div>
      </div>
    )
  }

  // 如果未認證，顯示重定向提示
  if (!isAuthenticated) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <div className="text-center space-y-4">
          <p className="text-muted-foreground">正在重定向到登入頁面...</p>
        </div>
      </div>
    )
  }

  // 以下是原始的 AdminRolesPage 內容，只有在確認用戶已登入後才會渲染
  return <AdminRolesContent />
}

// 將主要內容分離到單獨的組件，只有在用戶已登入時才會渲染
function AdminRolesContent() {
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const router = useRouter()

  useEffect(() => {
    loadData()
  }, [])

  async function loadData() {
    setIsLoading(true)
    setError(null)

    try {
      // 添加超時處理和更好的錯誤處理
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超時

      const rolesResponse = await fetch('/api/admin/roles', {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      console.log("Admin roles: API 響應狀態", {
        status: rolesResponse.status,
        statusText: rolesResponse.statusText,
        ok: rolesResponse.ok
      })

      if (rolesResponse.status === 401) {
        // 認證失敗，重定向到登入頁
        router.replace('/auth/login')
        return
      }

      if (rolesResponse.status === 403) {
        setHasPermission(false)
        setError('權限不足，無法訪問管理面板')
        return
      }

      if (!rolesResponse.ok) {
        throw new Error(`API 響應錯誤: ${rolesResponse.status} ${rolesResponse.statusText}`)
      }

      const rolesData = await rolesResponse.json()

      console.log("Admin roles: API 響應結果", {
        success: rolesData.success,
        dataLength: rolesData.data?.length,
        error: rolesData.error
      })

      if (rolesData.success) {
        setRoles(rolesData.data)
        setHasPermission(true)
      } else {
        throw new Error(rolesData.error || '獲取角色列表失敗')
      }

    } catch (error: any) {
      if (error.name === 'AbortError') {
        setError('請求超時，請檢查網路連線')
      } else {
        console.error('載入數據時出錯:', error)
        setError(error.message)
      }
    } finally {
      setIsLoading(false)
    }
  }

  function getRoleBadgeVariant(level: number) {
    if (level >= 100) return "destructive" // 站長
    if (level >= 50) return "default" // 主題版主
    if (level >= 25) return "secondary" // 子主題版主
    return "outline" // 其他
  }

  function getRoleIcon(roleKey: string) {
    switch (roleKey) {
      case 'owner':
        return <Shield className="h-4 w-4" />
      case 'topic_mod':
        return <Users className="h-4 w-4" />
      case 'subtopic_mod':
        return <Settings className="h-4 w-4" />
      default:
        return null
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">載入角色管理資料中...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className={hasPermission === false ? "text-destructive" : "text-destructive"}>
              {hasPermission === false ? "權限不足" : "載入失敗"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{error}</p>
            {hasPermission === false ? (
              <Button onClick={() => router.push('/')} className="w-full">
                返回首頁
              </Button>
            ) : (
              <Button onClick={loadData} className="w-full">
                重新載入
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">管理面板</h1>
        <p className="text-muted-foreground">
          管理系統角色、權限設定和內容審核
        </p>
      </div>

      <Tabs defaultValue="roles" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            角色管理
          </TabsTrigger>
          <TabsTrigger value="content-review" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            內容審核
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 角色列表 */}
            <Card>
              <CardHeader>
                <CardTitle>系統角色</CardTitle>
                <CardDescription>
                  查看所有可用的系統角色和權限層級
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {roles.map((role) => (
                    <div
                      key={role.role_key}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getRoleIcon(role.role_key)}
                        <div>
                          <h3 className="font-medium">{role.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {role.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getRoleBadgeVariant(role.level)}>
                          等級 {role.level}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 權限說明 */}
            <Card>
              <CardHeader>
                <CardTitle>權限說明</CardTitle>
                <CardDescription>
                  各角色的權限範圍說明
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Shield className="h-4 w-4 text-destructive" />
                      <h3 className="font-medium">站長 (Owner)</h3>
                    </div>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 管理所有內容和用戶</li>
                      <li>• 指派和移除版主</li>
                      <li>• 管理主題和子主題</li>
                      <li>• 系統設定管理</li>
                      <li>• 審核所有待審核內容</li>
                    </ul>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Users className="h-4 w-4" />
                      <h3 className="font-medium">主題版主 (Topic Moderator)</h3>
                    </div>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 管理特定主題下的內容</li>
                      <li>• 審核該主題的待審核內容</li>
                      <li>• 管理子主題</li>
                      <li>• 編輯他人內容</li>
                    </ul>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Settings className="h-4 w-4" />
                      <h3 className="font-medium">子主題版主 (Subtopic Moderator)</h3>
                    </div>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 管理特定子主題下的內容</li>
                      <li>• 審核該子主題的待審核內容</li>
                      <li>• 基本內容管理</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
              <CardDescription>
                常用的管理操作
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  className="h-20 flex-col space-y-2"
                  onClick={() => router.push('/admin/users')}
                >
                  <Users className="h-6 w-6" />
                  <span>用戶管理</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex-col space-y-2"
                  onClick={() => router.push('/admin/topics')}
                >
                  <Shield className="h-6 w-6" />
                  <span>主題管理</span>
                </Button>

                <Button
                  variant="outline"
                  className="h-20 flex-col space-y-2"
                  onClick={() => router.push('/admin/settings')}
                >
                  <Settings className="h-6 w-6" />
                  <span>系統設定</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content-review">
          <ContentReview />
        </TabsContent>
      </Tabs>
    </div>
  )
}
