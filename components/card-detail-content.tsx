"use client"
import { <PERSON>, <PERSON>bulb, Message<PERSON><PERSON>re, <PERSON><PERSON>zle, <PERSON><PERSON><PERSON><PERSON>, Thum<PERSON>Up, ThumbsDown } from "lucide-react"
import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/components/ui/use-toast"
import { formatDistanceToNow } from "date-fns"
import { zhTW } from "date-fns/locale"
import type { Card as CardType, Comment as CommentType, Profile } from "@/lib/types"
import { addComment, getCommentReferences } from "@/lib/interaction-service"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { BookMarked, ExternalLink, Eye, Flag, Share2, Sparkles, Tag, Reply, X } from "lucide-react"
import { RichTextContent } from "@/components/rich-text-content"
import { useRouter, useSearchParams } from "next/navigation"
import { CardQuote } from "@/components/card-quote"
import { CardMention } from "@/components/card-mention"
import { CardSelector } from "@/components/card-selector"



// 使用統一的語義類型配置
import { getSemanticTypeConfig } from "@/lib/constants"

interface CardDetailContentProps {
  card: CardType
  initialAuthState?: boolean
}

interface TagProps {
  parentTopic: string
  parentSubtopic: string
}

// 截斷文本函數
function truncateText(text: string, maxLength = 150) {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + "..."
}

// 構建回覆樹結構的輔助函數
function buildCommentTree(comments: any[]) {
  const commentMap = new Map()
  const rootComments: any[] = []

  // 首先將所有評論放入 Map 中
  comments.forEach((comment) => {
    comment.replies = [] // 使用 replies 而不是 children
    commentMap.set(comment.id, comment)
  })

  // 然後構建樹結構
  comments.forEach((comment) => {
    if (comment.parent_comment_id) {
      const parent = commentMap.get(comment.parent_comment_id)
      if (parent) {
        parent.replies.push(comment) // 使用 replies 而不是 children
      } else {
        rootComments.push(comment)
      }
    } else {
      rootComments.push(comment)
    }
  })

  return rootComments
}

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

export function CardDetailContent({ card, initialAuthState }: CardDetailContentProps) {
  const [comment, setComment] = useState("")
  const { user, isAuthenticated } = useAuth()
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const { toast } = useToast()
  const [collections, setCollections] = useState<any[]>([])
  const [newCollectionName, setNewCollectionName] = useState("")
  const [newCollectionDescription, setNewCollectionDescription] = useState("")
  const [isCreatingCollection, setIsCreatingCollection] = useState(false)
  const [isAddingToCollection, setIsAddingToCollection] = useState(false)
  const [selectedCollectionId, setSelectedCollectionId] = useState<string | null>(null)
  const [showNewCollectionForm, setShowNewCollectionForm] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [quotedCard, setQuotedCard] = useState<any | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isClient, setIsClient] = useState(false)
  const commentBoxRef = useRef<HTMLDivElement>(null)

  // 在現有的 useState 聲明下方添加
  const [likedCommentIds, setLikedCommentIds] = useState<Set<string>>(new Set());
  const [dislikedCommentIds, setDislikedCommentIds] = useState<Set<string>>(new Set());
  const [comments, setComments] = useState<CommentType[]>(card.replies || [])
  const [commentReferences, setCommentReferences] = useState<Record<string, any>>({})
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [quotedContent, setQuotedContent] = useState<string>("")
  const [collapsedComments, setCollapsedComments] = useState<string[]>([])

  const [likeCount, setLikeCount] = useState(card.stats?.likes || 0)
  const [dislikeCount, setDislikeCount] = useState(card.stats?.dislikes || 0)
  const [hasLiked, setHasLiked] = useState(false)
  const [hasDisliked, setHasDisliked] = useState(false)
  const [isProcessingReaction, setIsProcessingReaction] = useState(false)
  const [pendingReactions, setPendingReactions] = useState<Set<string>>(new Set())
  const [showCardMention, setShowCardMention] = useState(false)

  // 在組件頂部添加 router 和 searchParams
  const router = useRouter()
  const searchParams = useSearchParams()

  // 讀取來源頁面信息
  const fromTopic = searchParams.get('from_topic')
  const fromSubtopic = searchParams.get('from_subtopic')

  // 在客戶端渲染後設置 isClient 為 true
  useEffect(() => {
    setIsClient(true)

    setComments(card.replies || [])
  }, [card.replies])

  // 獲取評論的引用卡片
  useEffect(() => {
    if (comments.length > 0 && isClient) {
      const fetchCommentReferences = async () => {
        try {
          const commentIds = comments.map((c) => c.id)
          const { success, data } = await getCommentReferences(commentIds)
          if (success && data) {
            setCommentReferences(data)
          }
        } catch (error) {
          console.error("Error fetching comment references:", error)
        }
      }

      fetchCommentReferences()
    }
  }, [comments, isClient])

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: zhTW })
    } catch (error) {
      return dateString
    }
  }

  // 處理卡片引用
  const handleQuoteCard = (card: any) => {
    setQuotedCard(card)

    // 聚焦到文本框
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }

  // 處理卡片選擇 (從 CardMention 組件)
  const handleSelectCard = (selectedCard: any) => {
    setQuotedCard(selectedCard)
    setShowCardMention(false)

    // 聚焦到文本框
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }

  // 清除卡片引用
  const clearCardQuote = () => {
    setQuotedCard(null)
  }

  // 獲取評論內容
  const getCommentContent = (commentId: string | null): string => {
    if (!commentId) return ""
    const findCommentRecursive = (searchComments: CommentType[]): CommentType | undefined => {
      for (const c of searchComments) {
        if (c.id === commentId) return c
        if (c.replies && c.replies.length > 0) {
          const foundInChild = findCommentRecursive(c.replies)
          if (foundInChild) return foundInChild
        }
      }
      return undefined
    }
    const targetComment = findCommentRecursive(comments)
    return targetComment?.content || ""
  }

  // 獲取評論作者
  const getCommentAuthor = (commentId: string | null): string => {
    if (!commentId) return "未知用戶"
    const findCommentRecursive = (searchComments: CommentType[]): CommentType | undefined => {
      for (const c of searchComments) {
        if (c.id === commentId) return c
        if (c.replies && c.replies.length > 0) {
          const foundInChild = findCommentRecursive(c.replies)
          if (foundInChild) return foundInChild
        }
      }
      return undefined
    }
    const targetComment = findCommentRecursive(comments)
    return targetComment?.author?.name || "未知用戶"
  }

  // 處理回覆某人
  const handleReplyTo = (commentId: string) => {
    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能回覆",
        variant: "destructive",
      })
      return
    }

    const contentToQuote = getCommentContent(commentId)
    const authorToQuote = getCommentAuthor(commentId)
    setQuotedContent(`> 回覆 ${authorToQuote}:
> ${truncateText(contentToQuote, 50)}

`)

    // 設置回覆目標 - 這是關鍵！
    setReplyingTo(commentId)

    if (textareaRef.current) {
      textareaRef.current.focus()
      // Set selection to the end after setting content
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.value = quotedContent + textareaRef.current.value.replace(quotedContent, '');
          textareaRef.current.selectionStart = textareaRef.current.selectionEnd = textareaRef.current.value.length;
        }
      }, 0);
    }
  }

  // 清除引用
  const clearQuote = () => {
    setReplyingTo(null);
    setQuotedContent("");
    if (textareaRef.current) {
      // Clear only the quoted part if present
      // Use String literal for regex to avoid issues with backslashes and newlines in some environments.
      const quoteRegex = new RegExp("^> .+\n(> .+\n\n)?");
      textareaRef.current.value = textareaRef.current.value.replace(quoteRegex, "");
    }
  };

  // 處理摺疊/展開回覆
  const toggleCollapseComment = (commentId: string) => {
    setCollapsedComments((prev) =>
      prev.includes(commentId) ? prev.filter((id) => id !== commentId) : [...prev, commentId]
    )
  }

  // 處理提交留言
  const handleSubmitComment = async () => {
    if (!comment.trim() && !quotedCard) {
      toast({
        title: "留言不能為空",
        variant: "destructive",
      })
      return
    }

    setIsSubmittingComment(true)
    try {
      // Construct the payload for addComment service
      const commentPayload = {
        itemType: "card" as "card" | "thread", // Explicitly type for the service
        itemId: card.id,
        content: comment.trim(),
        parentCommentId: replyingTo, // replyingTo is string | null, assuming service handles string UUIDs
        referencedCard: quotedCard ? { id: quotedCard.id, type: "quote" } : null // Assuming type "quote"
      };

      const result = await addComment(commentPayload)

      if (result.success && result.data) {
        // The data from addComment in interaction-service is a bit different from CommentType
        // We need to adapt it or ensure addComment returns a full CommentType object
        // For now, let's assume result.data has enough info to construct a displayable comment
        // or we rely on a re-fetch/re-validation of the card data to get updated replies.

        // To properly update UI, the new comment from result.data should match CommentType
        // or be transformed. The current result.data structure from interaction-service:
        // { id, content, created_at, parent_comment_id, author: { name, avatar }, referenced_card }
        // CommentType expects: { id, author_id, content, ..., author: Profile, likes, dislikes, replies }

        // Simplest approach for now: show toast and let data revalidation handle UI update
        // This avoids complex client-side state manipulation if the returned object is not a full CommentType.

        // Example of manually creating a local comment object if needed (might lack some fields):
        // const newCommentEntry: CommentType = {
        //   id: result.data.id,
        //   author_id: user!.id, // This might not be in result.data directly
        //   content: result.data.content,
        //   created_at: result.data.created_at,
        //   parent_comment_id: result.data.parent_comment_id,
        //   author: { 
        //       id: user!.id, // Need full profile info
        //       name: result.data.author.name, 
        //       avatar: result.data.author.avatar, 
        //       email: user!.email || "", 
        //       created_at: user!.created_at || new Date().toISOString() 
        //   },
        //   likes: 0,
        //   dislikes: 0,
        //   replies: [],
        //   root_item_id: card.id, 
        //   root_item_type: "card",
        //   // quotedCard: result.data.referenced_card ? adaptedReferencedCard : undefined
        // };
        // setComments(prev => [newCommentEntry, ...prev]); // Or add to tree structure

        setComment("")
        setQuotedContent("")
        setReplyingTo(null)
        setQuotedCard(null)
        toast({ title: "評論已送出" })

        // It's often better to trigger a re-fetch of the card data to get the latest comments
        // This ensures consistency. For Next.js, router.refresh() can re-run server components data.
        router.refresh(); // Re-fetches data for the current route

      } else {
        toast({
          title: "留言提交失敗",
          description: result.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error submitting comment:", error)
      toast({
        title: "留言提交失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsSubmittingComment(false)
    }
  }

  // 添加以下函數來處理點讚/不認同，放在其他處理函數（如 handleBookmark）附近
  // 處理點讚/不認同
  const handleReaction = async (reactionType: "like" | "dislike", e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能進行此操作",
        variant: "destructive",
      })
      return
    }

    if (pendingReactions.has(reactionType)) {
      return
    }

    // 確保 id 是字符串並檢查格式
    const itemId = typeof card.id === "number" ? card.id.toString() : card.id
    if (!isValidUUID(itemId)) {
      console.log("跳過反應操作：無效的 UUID 格式", itemId)
      return
    }

    setPendingReactions((prev) => new Set(prev).add(reactionType))

    const wasLiked = hasLiked
    const wasDisliked = hasDisliked
    const prevLikeCount = likeCount
    const prevDislikeCount = dislikeCount

    // 樂觀更新 UI
    if (reactionType === "like") {
      setHasLiked(!wasLiked)
      setLikeCount((prev) => (wasLiked ? Math.max(0, prev - 1) : prev + 1))
      if (hasDisliked) {
        setHasDisliked(false)
        setDislikeCount((prev) => Math.max(0, prev - 1))
      }
    } else {
      setHasDisliked(!wasDisliked)
      setDislikeCount((prev) => (wasDisliked ? Math.max(0, prev - 1) : prev + 1))
      if (hasLiked) {
        setHasLiked(false)
        setLikeCount((prev) => Math.max(0, prev - 1))
      }
    }

    try {
      const response = await fetch("/api/reactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemType: "card",
          itemId: itemId,
          reactionType,
        }),
      })

      const data = await response.json()

      if (!response.ok || !data.success) {
        // 如果請求失敗，恢復原來的狀態
        setHasLiked(wasLiked)
        setHasDisliked(wasDisliked)
        setLikeCount(prevLikeCount)
        setDislikeCount(prevDislikeCount)

        toast({
          title: "操作失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error toggling reaction:", error)

      // 如果請求失敗，恢復原來的狀態
      setHasLiked(wasLiked)
      setHasDisliked(wasDisliked)
      setLikeCount(prevLikeCount)
      setDislikeCount(prevDislikeCount)

      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      // 從待處理集合中移除此操作
      setPendingReactions((prev) => {
        const newSet = new Set(prev)
        newSet.delete(reactionType)
        return newSet
      })
    }
  }

  // 處理留言點讚
  const handleCommentLike = async (commentId: string) => {
    if (!isAuthenticated || !user) {
      toast({ title: "請先登入", variant: "destructive" })
      return
    }
    if (pendingReactions.has(`like-${commentId}`)) return;

    setPendingReactions(prev => new Set(prev).add(`like-${commentId}`));

    const wasLiked = likedCommentIds.has(commentId);
    const wasDisliked = dislikedCommentIds.has(commentId);

    // Optimistically update UI
    setLikedCommentIds(prev => {
      const next = new Set(prev);
      if (wasLiked) {
        next.delete(commentId);
      } else {
        next.add(commentId);
      }
      return next;
    });
    if (wasDisliked) {
      setDislikedCommentIds(prev => {
        const next = new Set(prev);
        next.delete(commentId);
        return next;
      });
    }

    setComments(prevComments =>
      prevComments.map(c => {
        if (c.id === commentId) {
          let newLikes = c.likes || 0;
          let newDislikes = c.dislikes || 0;

          if (wasLiked) {
            newLikes = Math.max(0, newLikes - 1);
          } else {
            newLikes += 1;
            if (wasDisliked) {
              newDislikes = Math.max(0, newDislikes - 1);
            }
          }
          return { ...c, likes: newLikes, dislikes: newDislikes };
        }
        return c;
      })
    );

    try {
      // const response = await likeComment(commentId, user.id); // API call
      // Simulate API call success for now
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
      // if (response.success) {
      //   // API call was successful, local state is already updated.
      //   // Optionally update counts from server response if they differ significantly
      //   // For example, if other users reacted concurrently.
      // } else {
      //   // Revert optimistic update on failure
      //   setLikedCommentIds(prev => {
      //     const next = new Set(prev);
      //     if (wasLiked) next.add(commentId); else next.delete(commentId);
      //     return next;
      //   });
      //   if (wasDisliked) { // If it was disliked and we removed it
      //      setDislikedCommentIds(prev => new Set(prev).add(commentId));
      //   }
      //   setComments(prevComments =>
      //     prevComments.map(c => {
      //       if (c.id === commentId) {
      //            let newLikes = c.likes || 0;
      //            let newDislikes = c.dislikes || 0;
      //            if(wasLiked) newLikes +=1; else newLikes = Math.max(0, newLikes-1);
      //            if(wasDisliked && !wasLiked) newDislikes +=1; // only add back if it was only disliked
      //           return { ...c, likes: newLikes, dislikes: newDislikes };
      //       }
      //       return c;
      //     })
      //   );
      //   toast({ title: "點讚失敗", description: response.error, variant: "destructive" });
      // }
    } catch (error) {
      console.error("Error liking comment:", error);
      // Revert optimistic update on error
      setLikedCommentIds(prev => {
        const next = new Set(prev);
        if (wasLiked) next.add(commentId); else next.delete(commentId);
        return next;
      });
      if (wasDisliked) {
        setDislikedCommentIds(prev => new Set(prev).add(commentId));
      }
      setComments(prevComments =>
        prevComments.map(c => {
          if (c.id === commentId) {
            // Revert logic here needs to be careful
            // This simple revert might not handle all edge cases correctly without server truth
            let currentLikes = c.likes || 0;
            let currentDislikes = c.dislikes || 0;
            if (wasLiked) currentLikes += 1; else currentLikes = Math.max(0, currentLikes - 1);
            if (wasDisliked && !wasLiked) currentDislikes += 1;

            return { ...c, likes: currentLikes, dislikes: currentDislikes };
          }
          return c;
        })
      );
      toast({ title: "點讚操作出錯", variant: "destructive" });
    } finally {
      setPendingReactions(prev => {
        const next = new Set(prev);
        next.delete(`like-${commentId}`);
        return next;
      });
    }
  };

  // 處理留言倒讚
  const handleCommentDislike = async (commentId: string) => {
    if (!isAuthenticated || !user) {
      toast({ title: "請先登入", variant: "destructive" })
      return
    }
    if (pendingReactions.has(`dislike-${commentId}`)) return;

    setPendingReactions(prev => new Set(prev).add(`dislike-${commentId}`));

    const wasDisliked = dislikedCommentIds.has(commentId);
    const wasLiked = likedCommentIds.has(commentId);

    // Optimistic update
    setDislikedCommentIds(prev => {
      const next = new Set(prev);
      if (wasDisliked) {
        next.delete(commentId);
      } else {
        next.add(commentId);
      }
      return next;
    });
    if (wasLiked) {
      setLikedCommentIds(prev => {
        const next = new Set(prev);
        next.delete(commentId);
        return next;
      });
    }

    setComments(prevComments =>
      prevComments.map(c => {
        if (c.id === commentId) {
          let newLikes = c.likes || 0;
          let newDislikes = c.dislikes || 0;

          if (wasDisliked) {
            newDislikes = Math.max(0, newDislikes - 1);
          } else {
            newDislikes += 1;
            if (wasLiked) {
              newLikes = Math.max(0, newLikes - 1);
            }
          }
          return { ...c, likes: newLikes, dislikes: newDislikes };
        }
        return c;
      })
    );

    try {
      // const response = await dislikeComment(commentId, user.id); // API call
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
      // if (response.success) {
      //   // API success
      // } else {
      //   // Revert
      //   setDislikedCommentIds(prev => {
      //       const next = new Set(prev);
      //       if(wasDisliked) next.add(commentId); else next.delete(commentId);
      //       return next;
      //   });
      //   if(wasLiked){
      //       setLikedCommentIds(prev => new Set(prev).add(commentId));
      //   }
      //   // Revert comment counts
      //   toast({ title: "操作失敗", description: response.error, variant: "destructive" });
      // }
    } catch (error) {
      // Revert
      console.error("Error disliking comment:", error);
      // Simplified revert, actual revert logic might need to be more robust
      setDislikedCommentIds(prev => {
        const next = new Set(prev);
        if (wasDisliked) next.add(commentId); else next.delete(commentId);
        return next;
      });
      if (wasLiked) {
        setLikedCommentIds(prev => new Set(prev).add(commentId));
      }
      // Revert comment counts
      toast({ title: "操作出錯", variant: "destructive" });
    } finally {
      setPendingReactions(prev => {
        const next = new Set(prev);
        next.delete(`dislike-${commentId}`);
        return next;
      });
    }
  };

  // 獲取用戶反應狀態 - 使用傳入的 stats 數據
  useEffect(() => {
    if (!isAuthenticated || !isClient) {
      setHasLiked(false)
      setHasDisliked(false)
      return
    }

    // 優先使用傳入的 stats 數據中的用戶狀態
    if (card.stats?.hasLiked !== undefined) {
      setHasLiked(card.stats.hasLiked)
    }
    if (card.stats?.hasDisliked !== undefined) {
      setHasDisliked(card.stats.hasDisliked)
    }

    // 如果沒有完整的用戶狀態數據，使用舊的單獨 API 作為後備
    if (card.stats?.hasLiked === undefined || card.stats?.hasDisliked === undefined) {
      const fetchReactionStatus = async () => {
        try {
          const itemId = typeof card.id === "number" ? card.id.toString() : card.id
          if (!isValidUUID(itemId)) {
            console.log("跳過反應狀態檢查：無效的 UUID 格式", itemId)
            return
          }

          if (card.stats?.hasLiked === undefined) {
            const likeRes = await fetch(`/api/reactions/check?itemType=card&itemId=${itemId}&reactionType=like`)
            if (likeRes.ok) {
              const likeData = await likeRes.json()
              if (likeData.success) {
                setHasLiked(!!likeData.data.hasReacted)
              }
            }
          }

          if (card.stats?.hasDisliked === undefined) {
            const dislikeRes = await fetch(`/api/reactions/check?itemType=card&itemId=${itemId}&reactionType=dislike`)
            if (dislikeRes.ok) {
              const dislikeData = await dislikeRes.json()
              if (dislikeData.success) {
                setHasDisliked(!!dislikeData.data.hasReacted)
              }
            }
          }
        } catch (error) {
          console.error("Error checking reaction status:", error)
        }
      }
      fetchReactionStatus()
    }
  }, [card.id, isAuthenticated, isClient, card.stats?.hasLiked, card.stats?.hasDisliked])

  // 使用傳入的 stats 數據初始化反應計數
  useEffect(() => {
    if (!isClient) return

    // 優先使用傳入的 stats 數據
    if (card.stats) {
      setLikeCount(card.stats.likes || 0)
      setDislikeCount(card.stats.dislikes || 0)
    } else {
      // 如果沒有 stats 數據，使用舊的單獨 API 作為後備
      const fetchReactionCounts = async () => {
        try {
          const itemId = typeof card.id === "number" ? card.id.toString() : card.id
          if (!isValidUUID(itemId)) {
            console.log("跳過反應計數獲取：無效的 UUID 格式", itemId)
            return
          }

          const countRes = await fetch(`/api/reactions/count?itemType=card&itemId=${itemId}`)
          if (!countRes.ok) {
            console.error("Error fetching reaction counts:", await countRes.text())
            return
          }

          const countData = await countRes.json()
          if (countData.success) {
            setLikeCount(countData.data.like || 0)
            setDislikeCount(countData.data.dislike || 0)
          }
        } catch (error) {
          console.error("Error fetching reaction counts:", error)
        }
      }
      fetchReactionCounts()
    }
  }, [card.id, isClient, card.stats?.likes, card.stats?.dislikes])

  // 使用傳入的 stats 數據初始化收藏狀態
  useEffect(() => {
    if (!isAuthenticated || !user) {
      setIsBookmarked(false)
      return
    }

    // 優先使用傳入的 stats 數據
    if (card.stats?.hasBookmarked !== undefined) {
      setIsBookmarked(card.stats.hasBookmarked)
    } else {
      // 如果沒有 stats 數據，使用舊的單獨 API 作為後備
      const checkBookmarkStatus = async () => {
        try {
          const response = await fetch(`/api/bookmarks/check?itemType=card&itemId=${card.id}`)
          if (response.ok) {
            const data = await response.json()
            if (data.success) {
              setIsBookmarked(data.data.isBookmarked)
            }
          }
        } catch (error) {
          console.error("Error checking bookmark status:", error)
        }
      }
      checkBookmarkStatus()
    }
  }, [card.id, isAuthenticated, user, card.stats?.hasBookmarked])

  // 獲取用戶的收藏牆
  useEffect(() => {
    if (isAuthenticated && user && dialogOpen) {
      const fetchCollections = async () => {
        try {
          const response = await fetch("/api/collections")
          if (response.ok) {
            const data = await response.json()
            if (data.success) {
              setCollections(data.data || [])
              if (data.data && data.data.length > 0) {
                setSelectedCollectionId(data.data[0].id)
              }
            }
          }
        } catch (error) {
          console.error("Error fetching collections:", error)
        }
      }
      fetchCollections()
    }
  }, [isAuthenticated, user, dialogOpen])

  // 處理收藏/取消收藏
  const handleBookmark = async () => {
    if (!isAuthenticated) {
      toast({
        title: "請先登入",
        description: "您需要登入才能收藏觀點卡",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      const method = isBookmarked ? "DELETE" : "POST"
      const response = await fetch("/api/bookmarks", {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemType: "card",
          itemId: card.id,
        }),
      })

      const data = await response.json()
      if (data.success) {
        setIsBookmarked(!isBookmarked)
        toast({
          title: isBookmarked ? "已取消收藏" : "收藏成功",
          description: isBookmarked ? "觀點卡已從您的收藏中移除" : "觀點卡已添加到您的收藏",
        })
      } else {
        toast({
          title: "操作失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error toggling bookmark:", error)
      toast({
        title: "操作失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 創建新收藏牆
  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) {
      toast({
        title: "請輸入收藏牆名稱",
        variant: "destructive",
      })
      return
    }

    setIsCreatingCollection(true)
    try {
      const response = await fetch("/api/collections", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newCollectionName,
          description: newCollectionDescription,
        }),
      })

      const data = await response.json()
      if (data.success && data.data) {
        toast({
          title: "創建成功",
          description: "收藏牆已創建",
        })
        setCollections([data.data, ...collections])
        setSelectedCollectionId(data.data.id)
        setShowNewCollectionForm(false)
        setNewCollectionName("")
        setNewCollectionDescription("")
      } else {
        toast({
          title: "創建失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating collection:", error)
      toast({
        title: "創建失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsCreatingCollection(false)
    }
  }

  // 添加到收藏牆
  const handleAddToCollection = async () => {
    if (!selectedCollectionId) {
      toast({
        title: "請選擇收藏牆",
        variant: "destructive",
      })
      return
    }

    setIsAddingToCollection(true)
    try {
      const response = await fetch(`/api/collections/${selectedCollectionId}/cards`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cardId: card.id,
        }),
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: "添加成功",
          description: "觀點卡已添加到收藏牆",
        })
        setDialogOpen(false)
      } else {
        toast({
          title: "添加失敗",
          description: data.error || "請稍後再試",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error adding to collection:", error)
      toast({
        title: "添加失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsAddingToCollection(false)
    }
  }

  // 監聽文本框輸入，檢測 @ 符號
  useEffect(() => {
    const handleInput = () => {
      if (textareaRef.current) {
        const text = textareaRef.current.value
        const cursorPosition = textareaRef.current.selectionStart
        const textBeforeCursor = text.substring(0, cursorPosition)

        // 檢查是否有 @ 符號
        const atIndex = textBeforeCursor.lastIndexOf("@")
        if (atIndex !== -1 && (atIndex === 0 || /\s/.test(textBeforeCursor[atIndex - 1]))) {
          // 提取 @ 後面的搜索詞
          const searchText = textBeforeCursor.substring(atIndex + 1)

          // 如果有 @ 且後面沒有空格，則顯示卡片選擇菜單
          if (!searchText.includes(" ")) {
            setShowCardMention(true)
          } else {
            setShowCardMention(false)
          }
        } else {
          setShowCardMention(false)
        }
      }
    }

    const textarea = textareaRef.current
    if (textarea) {
      textarea.addEventListener("input", handleInput)
      return () => {
        textarea.removeEventListener("input", handleInput)
      }
    }
  }, [])

  // 模擬數據 - 討論
  const discussions =
    comments.length > 0
      ? comments
      : [
        {
          id: 1,
          author: {
            name: "技術愛好者",
            avatar: "/mystical-forest-spirit.png",
          },
          content: "非常精彩的分析！我特別認同關於通訊協議設計的部分，這確實是多智能體系統的核心挑戰。",
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          quotedCard: null,
        },
        {
          id: 2,
          author: {
            name: "AI研究員",
            avatar: "/diverse-research-team.png",
          },
          content:
            "我在實際項目中也遇到了類似的問題。想請教一下，你們是如何處理智能體之間的衝突解決的？特別是當多個智能體對同一資源有競爭需求時。",
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          quotedCard: {
            id: 102,
            type: "implementation",
            title: "如何實現高效的向量檢索",
            content: "向量檢索是 RAG 系統的核心組件，本文介紹幾種常用的向量檢索方法及其優缺點...",
            tags: ["向量檢索", "FAISS", "Pinecone"],
            author: "系統架構師",
          },
        },
        {
          id: 3,
          author: {
            name: "系統架構師",
            avatar: "/modern-architect-studio.png",
          },
          content:
            "對於衝突解決，我們採用了基於優先級和資源預約的機制。高優先級任務可以預先分配資源，而對於同級任務則使用基於時間戳的先到先得策略。此外，我們還實現了資源釋放的超時機制，避免死鎖。",
          created_at: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString(),
          parent_comment_id: 2,
          quotedCard: null,
        },
      ]

  useEffect(() => {
    // 確保統計數據存在
    if (!card.stats) {
      card.stats = {
        views: Math.floor(Math.random() * 1000) + 100,
        likes: Math.floor(Math.random() * 100) + 10,
        dislikes: Math.floor(Math.random() * 20),
        comments: comments.length,
        bookmarks: 0,
      }
    }
  }, [card, comments.length])

  // 提取主題和子主題
  const mainTopic =
    card.topics && card.topics.length > 0
      ? { name: card.topics[0].name || "未分類", id: card.topics[0].id || "0", slug: card.topics[0].slug }
      : { name: "未分類", id: "0", slug: "general" }

  const subtopics = card.subtopics || []
  const tags =
    subtopics.length > 0 ? subtopics.map((s) => (typeof s === "string" ? s : s.name || "")) : []

  // 將 fromSubtopic (可能是 slug) 轉換為對應的 name
  const getSubtopicDisplayName = (subtopicSlugOrName: string) => {
    if (!subtopicSlugOrName) return ""

    // 嘗試在 subtopics 中找到匹配的項目
    const matchedSubtopic = subtopics.find((s) => {
      if (typeof s === "string") return s === subtopicSlugOrName
      return s.slug === subtopicSlugOrName || s.name === subtopicSlugOrName
    })

    if (matchedSubtopic) {
      return typeof matchedSubtopic === "string" ? matchedSubtopic : matchedSubtopic.name || subtopicSlugOrName
    }

    // 如果找不到匹配項，返回原始值（可能已經是 name）
    return subtopicSlugOrName
  }

  // 確保 author 是有效的對象
  const author = card.author || { name: "未知作者", avatar: "" }
  const authorName = typeof author === "string" ? author : author.name || "未知作者"
  const authorAvatar = typeof author === "string" ? "" : author.avatar || ""

  // 構建評論樹
  const commentTree = buildCommentTree(comments)

  // 渲染單個評論及其子評論
  const renderComment = (commentToRender: CommentType, index: number, level = 0) => {
    const isCollapsed = collapsedComments.includes(commentToRender.id)
    const hasChildren = commentToRender.replies && commentToRender.replies.length > 0
    const referencedCard = commentReferences[commentToRender.id] || commentToRender.quotedCard || null

    // For optimistic UI, we can derive from local state or check a specific field from server
    const userHasLiked = likedCommentIds.has(commentToRender.id);
    const userHasDisliked = dislikedCommentIds.has(commentToRender.id);

    return (
      <div key={commentToRender.id} id={`comment-${commentToRender.id}`} className="relative">
        <Card
          className={cn(
            "mb-3",
            level > 0 && "ml-6 border-l-4 border-l-primary/20 relative",
            level > 1 && "ml-12",
            level > 2 && "ml-16",
            level > 3 && "ml-20",
          )}
        >
          {level > 0 && <div className="absolute -left-[2px] top-0 bottom-0 w-1 bg-primary/20"></div>}
          <CardContent className={cn("p-4", level > 0 && "border-l-4 border-l-primary/10")}>
            <div className="flex items-start gap-3">
              <Avatar>
                <AvatarImage
                  src={
                    commentToRender.author?.avatar ||
                    `/placeholder.svg?height=40&width=40&query=${commentToRender.author?.name || "未知"}`
                  }
                  alt={commentToRender.author?.name || "未知"}
                />
                <AvatarFallback>{(commentToRender.author?.name || "未知").substring(0, 2).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="font-medium">{commentToRender.author?.name || "未知"}</div>
                    {level === 0 && (
                      <div className="text-xs px-2 py-0.5 bg-primary text-primary-foreground font-medium rounded-full">
                        #{index + 1}樓
                      </div>
                    )}
                    {commentToRender.parent_comment_id && (
                      <div className="text-xs text-muted-foreground">
                        回覆 <span className="font-medium">{getCommentAuthor(commentToRender.parent_comment_id)}</span>
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">{formatDate(commentToRender.created_at)}</div>
                </div>

                {/* 顯示引用的卡片 */}
                {referencedCard && (
                  <div className="mt-3 mb-4">
                    <CardQuote card={referencedCard} className="border-l-primary/70" />
                  </div>
                )}

                <div className="mt-2 text-sm whitespace-pre-line">{commentToRender.content}</div>

                {/* 評論點讚倒讚和回覆按鈕 */}
                <div className="mt-3 flex items-center gap-2">
                  {isClient && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "flex items-center gap-1 h-7 px-2 text-xs",
                          userHasLiked && "text-primary",
                        )}
                        onClick={() => handleCommentLike(commentToRender.id)}
                      >
                        <ThumbsUp className={cn("h-3.5 w-3.5", userHasLiked && "fill-current")} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "flex items-center gap-1 h-7 px-2 text-xs",
                          userHasDisliked && "text-primary",
                        )}
                        onClick={() => handleCommentDislike(commentToRender.id)}
                      >
                        <ThumbsDown
                          className={cn("h-3.5 w-3.5", userHasDisliked && "fill-current")}
                        />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1 h-7 px-2 text-xs"
                        onClick={() => handleReplyTo(commentToRender.id)}
                      >
                        <Reply className="h-3.5 w-3.5" />
                        <span>回覆</span>
                      </Button>
                    </>
                  )}

                  {hasChildren && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="flex items-center gap-1 h-7 px-2 text-xs"
                      onClick={() => toggleCollapseComment(commentToRender.id)}
                    >
                      {isCollapsed ? (
                        <>
                          <span>展開 {(commentToRender.replies || []).length} 則回覆</span>
                        </>
                      ) : (
                        <>
                          <span>收起回覆</span>
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {hasChildren && (
          <div className={cn("relative pl-6", isCollapsed && "hidden")}>
            {/* 添加連接線 */}
            <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-primary/10"></div>

            {(commentToRender.replies || []).map((childComment: CommentType, childIndex: number) =>
              renderComment(childComment, childIndex, level + 1),
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="max-w-[1400px] mx-auto px-4 space-y-6">
      {/* 麵包屑導航 */}
      <nav className="flex items-center text-sm text-muted-foreground mb-4">
        <Link href="/" className="hover:text-foreground flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
          首頁
        </Link>

        {/* 根據來源信息動態構建麵包屑 */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 mx-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
        <Link
          href={`/topic/${fromTopic ? encodeURIComponent(fromTopic) : (mainTopic.slug || mainTopic.name.toLowerCase().replace(/\s+/g, "-"))}`}
          className="hover:text-foreground px-2 py-1 -mx-2 -my-1 rounded-md"
        >
          {fromTopic || mainTopic.name}
        </Link>

        {/* 優先使用來源 subtopic，否則使用卡片的第一個 subtopic */}
        {(fromSubtopic || (tags.length > 0 && !fromTopic)) && (
          <>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mx-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <Link
              href={`/topic/${fromTopic ? encodeURIComponent(fromTopic) : (mainTopic.slug || mainTopic.name.toLowerCase().replace(/\s+/g, "-"))}/${encodeURIComponent((fromSubtopic || tags[0]).toLowerCase())}`}
              className="hover:text-foreground px-2 py-1 -mx-2 -my-1 rounded-md"
            >
              #{fromSubtopic ? getSubtopicDisplayName(fromSubtopic) : tags[0]}
            </Link>
          </>
        )}

        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 mx-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
        <span className="font-medium text-foreground px-2 py-1 -mx-2 -my-1 rounded-md">觀點卡</span>
      </nav>

      {/* 卡片內容 */}
      <Card className="overflow-hidden bg-gray-50 dark:bg-gray-900">
        <CardHeader className="p-6 pb-4">
          <div className="flex flex-wrap items-center gap-2 mb-4">
            {/* 類型標籤 */}
            <Badge
              variant="outline"
              className={cn(
                "flex items-center gap-1",
                getSemanticTypeConfig(card.semantic_type).color,
              )}
            >
              {getSemanticTypeConfig(card.semantic_type).icon}
              <span>{getSemanticTypeConfig(card.semantic_type).label}</span>
            </Badge>

            {/* 主題標籤 */}
            <Badge variant="secondary" className="flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              <span>{mainTopic.name}</span>
            </Badge>

            {/* 領袖標籤 */}
            {card.contribution_type === "top_author" && (
              <Badge variant="default" className="flex items-center gap-1 bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                <Sparkles className="h-3 w-3" />
                <span>Leader</span>
              </Badge>
            )}
          </div>

          {/* 標題 */}
          <h1 className="text-3xl font-bold mb-4">{card.title}</h1>

          {/* 作者和日期 - 改為更現代的設計 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={authorAvatar || `/placeholder.svg?height=40&width=40&query=${authorName}`}
                  alt={authorName}
                />
                <AvatarFallback>{authorName.substring(0, 2).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{authorName}</div>
                <div className="text-sm text-muted-foreground">發布於: {formatDate(card.created_at)}</div>
              </div>
            </div>

            {/* 統計數據 */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1" title="觀看次數">
                <Eye className="h-4 w-4" />
                <span>{card.stats?.views || 0}</span>
              </div>
              <div className="flex items-center gap-1" title="留言">
                <MessageSquare className="h-4 w-4" />
                <span>{card.stats?.comments || 0}</span>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 pt-2">
          {/* 內容 - 使用富文本顯示 */}
          <div className="text-base mb-6 leading-relaxed">
            <RichTextContent content={card.content} />
          </div>

          {/* 來源 - 改進外部連結顯示 */}
          {card.original_url && (
            <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border mt-6">
              <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0" />
              <div className="flex-1">
                <div className="text-sm font-medium mb-1">原始出處</div>
                <a
                  href={card.original_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm flex items-center hover:text-primary transition-colors"
                >
                  <span className="truncate">{card.original_url}</span>
                  <ExternalLink className="h-3.5 w-3.5 ml-1 flex-shrink-0" />
                </a>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="p-6 pt-0 flex flex-col gap-4">
          {/* 標籤列 */}
          <div className="w-full pt-4 border-t">
            <div className="flex flex-wrap gap-2 mb-6">
              <Tag className="h-4 w-4 text-muted-foreground mr-1" />
              {tags.map((tag: string, index: number) => (
                <Link
                  key={index}
                  href={`/topic/${mainTopic.slug || mainTopic.name.toLowerCase().replace(/\s+/g, "-")}?subtopic=${encodeURIComponent(tag.toLowerCase())}`}>
                  <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
                    #{tag}
                  </Badge>
                </Link>
              ))}
            </div>

            {/* 操作按鈕 - 重新設計為更現代的佈局 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* 點讚倒讚按鈕 */}
                {isClient && (
                  <>
                    <Button
                      variant={hasLiked ? "default" : "outline"}
                      size="sm"
                      className={cn(
                        "flex items-center gap-2",
                        hasLiked && "bg-primary text-primary-foreground",
                        pendingReactions.has("like") && "opacity-70",
                      )}
                      onClick={(e) => handleReaction("like", e)}
                    >
                      <ThumbsUp className={cn("h-4 w-4", hasLiked && "fill-current")} />
                      <span>{likeCount}</span>
                    </Button>
                    <Button
                      variant={hasDisliked ? "default" : "outline"}
                      size="sm"
                      className={cn(
                        "flex items-center gap-2",
                        hasDisliked && "bg-primary text-primary-foreground",
                        pendingReactions.has("dislike") && "opacity-70",
                      )}
                      onClick={(e) => handleReaction("dislike", e)}
                    >
                      <ThumbsDown className={cn("h-4 w-4", hasDisliked && "fill-current")} />
                      <span>{dislikeCount}</span>
                    </Button>
                  </>
                )}

                <Button
                  variant={isBookmarked ? "default" : "outline"}
                  size="sm"
                  className={cn("flex items-center gap-2", isBookmarked && "bg-primary text-primary-foreground")}
                  onClick={handleBookmark}
                  disabled={isLoading || !isAuthenticated}
                >
                  <BookMarked className="h-4 w-4" />
                  <span>{isBookmarked ? "已收藏" : "收藏"}</span>
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Share2 className="h-4 w-4" />
                  <span>分享</span>
                </Button>

                <Button variant="ghost" size="sm" className="flex items-center gap-2 text-muted-foreground">
                  <Flag className="h-4 w-4" />
                  <span>檢舉</span>
                </Button>
              </div>
            </div>
          </div>
        </CardFooter>
      </Card>

      {/* 卡片留言區 */}
      <div className="space-y-4 pt-4" id="comments">
        <div className="flex justify-between items-center border-b pb-1 mb-4">
          <h2 className="text-xl font-bold flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            留言區
          </h2>
        </div>

        {/* 留言列表 */}
        <div className="space-y-4">
          {commentTree.length > 0 ? (
            commentTree.map((comment, index) => renderComment(comment, index))
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>暫無留言，成為第一個留言的人吧！</p>
            </div>
          )}
        </div>

        {/* 添加留言 */}
        <div className="pt-4" ref={commentBoxRef}>
          <h3 className="text-sm font-medium mb-2">
            {replyingTo !== null
              ? `回覆 ${getCommentAuthor(replyingTo)}`
              : isAuthenticated
                ? "添加留言"
                : "請先登入..."}
          </h3>

          {/* 引用內容區域 */}
          {quotedContent && (
            <div className="mb-4 relative">
              <div className="bg-muted p-3 rounded-md border-l-4 border-primary/50 text-sm text-muted-foreground">
                <button
                  type="button"
                  className="absolute top-1 right-1 h-5 w-5 p-0 flex items-center justify-center text-muted-foreground hover:text-foreground"
                  onClick={clearQuote}
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-medium mb-1">引用 {getCommentAuthor(replyingTo)} 的發言：</p>
                <p className="whitespace-pre-line">{truncateText(quotedContent, 200)}</p>
              </div>
            </div>
          )}

          {/* 引用卡片區域 */}
          {quotedCard && (
            <div className="mb-4 relative">
              <div className="bg-muted p-3 rounded-md border-l-4 border-primary/50">
                <button
                  type="button"
                  className="absolute top-1 right-1 h-5 w-5 p-0 flex items-center justify-center text-muted-foreground hover:text-foreground"
                  onClick={clearCardQuote}
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-medium mb-1 text-sm">引用卡片：</p>
                <CardQuote card={quotedCard} className="border-l-primary/70" />
              </div>
            </div>
          )}

          {replyingTo !== null && (
            <div className="mb-2 flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={() => {
                  setReplyingTo(null)
                  setQuotedContent("")
                }}
              >
                取消回覆
              </Button>
            </div>
          )}

          <div className="relative">
            <Textarea
              ref={textareaRef}
              placeholder={isAuthenticated ? "分享你的想法... 使用 @ 可以引用卡片" : "請先登入..."}
              className="min-h-[100px] mb-2"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              disabled={!isAuthenticated || isSubmittingComment}
            />

            {/* 卡片選擇菜單 */}
            {showCardMention && textareaRef.current && (
              <div className="relative">
                <CardMention textareaRef={textareaRef as React.RefObject<HTMLTextAreaElement>} onSelectCard={handleSelectCard} />
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button onClick={handleSubmitComment} disabled={!comment.trim() || !isAuthenticated || isSubmittingComment}>
              {isSubmittingComment ? "發布中..." : !isAuthenticated ? "請先登入" : "發布留言"}
            </Button>
            {isAuthenticated && <CardSelector onSelectCard={handleSelectCard} buttonText="引用卡片" />}
            <div className="text-xs text-muted-foreground">
              提示：使用 <span className="font-medium">@</span> 可以引用卡片
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
