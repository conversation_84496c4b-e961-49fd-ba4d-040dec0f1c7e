"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { BookMarked, MessageSquare, Plus, Star, FileText, BookOpen } from "lucide-react"
import Link from "next/link"
import { useState, useEffect, useRef } from "react"

// 在組件頂部添加 useSearchParams 和 useEffect
import { useSearchParams, useRouter } from "next/navigation"
import { TopicDataLoader } from "@/components/topic-data-loader"
import { ContentFilter, FilterTags } from "@/components/content-filter"
import { useContentFilter } from "@/hooks/use-content-filter"
import { SubscriptionButton } from "@/components/subscription-button"

interface TopicPageContentProps {
  topic: {
    id: string
    name: string
    description: string
    slug: string
    subtopics?: {
      id: string
      name: string
      description: string
      slug: string
    }[]
  }
  initialSubtopic?: string
}

export function TopicPageContent({ topic, initialSubtopic }: TopicPageContentProps) {
  const [activeSubtopic, setActiveSubtopic] = useState<string | null>(initialSubtopic || null)
  const [activeTab, setActiveTab] = useState<string>("viewpoints")
  const initialRenderRef = useRef(true)
  const subscriptionLoadingRef = useRef<string | null>(null)

  // 統計數據狀態
  const [stats, setStats] = useState({ cardsCount: 0, threadsCount: 0 })

  // 訂閱狀態管理
  const [subscriptionData, setSubscriptionData] = useState<{
    isSubscribed: boolean
    subscriberCount: number
    isLoading: boolean
  }>({
    isSubscribed: false,
    subscriberCount: 0,
    isLoading: true
  })

  // 添加過濾器功能 - 禁用自動載入，因為我們已經有 topic 數據
  const {
    filterState,
    filterActions,
    availableTopics,
    availableSubtopics,
    isLoadingFilters,
    hasActiveFilters
  } = useContentFilter({
    loadTopicsOnMount: false,
    loadSubtopicsOnMount: false
  })

  // 在 TopicPageContent 組件內部，添加以下代碼來處理 URL 查詢參數
  const searchParams = useSearchParams()
  const router = useRouter()
  const subtopicParam = searchParams.get("subtopic")

  // 使用 useEffect 來設置 activeSubtopic，但只在初始渲染時執行一次
  useEffect(() => {
    if (!initialRenderRef.current) return

    if (initialSubtopic) {
      // If an initial subtopic is provided, use it directly
      setActiveSubtopic(initialSubtopic)
      initialRenderRef.current = false
    } else if (subtopicParam) {
      // Otherwise check URL query parameters
      const normalizedSubtopic = subtopicParam.toLowerCase()
      const matchedSubtopic = topic.subtopics?.find((sub) => sub.slug.toLowerCase() === normalizedSubtopic)

      if (matchedSubtopic) {
        setActiveSubtopic(matchedSubtopic.name)
      } else {
        // If not in predefined subtopics, use it directly
        setActiveSubtopic(subtopicParam)
      }
      initialRenderRef.current = false
    } else {
      initialRenderRef.current = false
    }
  }, [initialSubtopic, subtopicParam, topic.subtopics])

  // 載入訂閱數據 - 使用批量 API，添加去重機制
  useEffect(() => {
    const loadSubscriptionData = async () => {
      const currentItemType = activeSubtopic ? "subtopic" : "topic"
      const currentItemId = activeSubtopic
        ? topic.subtopics?.find(sub => sub.name === activeSubtopic)?.id || ""
        : topic.id

      if (!currentItemId) {
        setSubscriptionData(prev => ({ ...prev, isLoading: false }))
        return
      }

      // 防止重複載入相同的數據
      const requestKey = `${currentItemType}-${currentItemId}`
      if (subscriptionLoadingRef.current === requestKey) {
        return
      }
      subscriptionLoadingRef.current = requestKey

      try {
        setSubscriptionData(prev => ({ ...prev, isLoading: true }))

        // 使用批量 API 一次獲取所有訂閱數據
        const response = await fetch('/api/subscriptions/batch', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            items: [{
              itemType: currentItemType,
              itemId: currentItemId
            }]
          })
        })

        const result = await response.json()

        if (result.success) {
          const statusData = result.data.statuses[currentItemId]
          const statsData = result.data.stats[currentItemId]

          setSubscriptionData({
            isSubscribed: statusData?.subscribed || false,
            subscriberCount: statsData?.subscriber_count || 0,
            isLoading: false
          })
        } else {
          console.error("載入訂閱數據失敗:", result.error)
          setSubscriptionData({
            isSubscribed: false,
            subscriberCount: 0,
            isLoading: false
          })
        }
      } catch (error) {
        console.error("載入訂閱數據錯誤:", error)
        setSubscriptionData({
          isSubscribed: false,
          subscriberCount: 0,
          isLoading: false
        })
      } finally {
        subscriptionLoadingRef.current = null
      }
    }

    loadSubscriptionData()
  }, [activeSubtopic, topic.id, topic.subtopics])

  // 修改 setActiveSubtopic 的點擊處理函數，使其更新 URL
  const handleSubtopicClick = (subtopic: string | null) => {
    if (subtopic === activeSubtopic) {
      // 如果點擊當前活動的子主題，則清除選擇
      setActiveSubtopic(null)
      router.push(`/topic/${topic.slug}`)
    } else {
      // 否則設置新的子主題
      setActiveSubtopic(subtopic)
      if (subtopic) {
        // Find the subtopic slug
        const subtopicObj = topic.subtopics?.find((sub) => sub.name === subtopic)
        const subtopicSlug = subtopicObj?.slug || subtopic.toLowerCase()

        // 使用 encodeURIComponent 確保中文字符在 URL 中被正確編碼
        router.push(`/topic/${topic.slug}/${encodeURIComponent(subtopicSlug)}`)
      } else {
        router.push(`/topic/${topic.slug}`)
      }
    }
  }

  const [isSticky, setIsSticky] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const grayTitleArea = document.querySelector(".bg-gray-50, .dark\\:bg-gray-900")
      const threshold = grayTitleArea ? grayTitleArea.getBoundingClientRect().bottom + window.scrollY : 300

      setIsSticky(scrollPosition > threshold)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <>
      {/* 非 sticky 內容區域 */}
      <div className="max-w-[1400px] mx-auto px-4 space-y-6">
        {/* 麵包屑導航 - 保持在灰底區上方 */}
        <nav className="flex items-center text-sm text-muted-foreground mb-4">
          <Link href="/" className="hover:text-foreground flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            首頁
          </Link>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mx-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
          <Link href={`/topic/${topic.slug}`} className="hover:text-foreground px-2 py-1 -mx-2 -my-1 rounded-md">
            {topic.name}
          </Link>
          {activeSubtopic && (
            <>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mx-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <Link
                href={`/topic/${topic.slug}/${encodeURIComponent(
                  topic.subtopics?.find((sub) => sub.name === activeSubtopic)?.slug || activeSubtopic.toLowerCase(),
                )}`}
                className="font-medium text-foreground px-2 py-1 -mx-2 -my-1 rounded-md"
              >
                #{activeSubtopic}
              </Link>
            </>
          )}
        </nav>

        {/* 主題標題、描述和子主題篩選列 */}
        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
          {/* 主題標題和描述 */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold flex items-center">
                {activeSubtopic ? <span>#{activeSubtopic}</span> : <span>{topic.name}</span>}
              </h1>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="flex items-center text-blue-500">
                    <BookOpen className="h-5 w-5 mr-1" />
                    <span className="font-medium">{stats.cardsCount}</span>
                    <span className="text-sm text-muted-foreground ml-1">卡片</span>
                  </div>
                  <div className="flex items-center text-green-500">
                    <MessageSquare className="h-5 w-5 mr-1" />
                    <span className="font-medium">{stats.threadsCount}</span>
                    <span className="text-sm text-muted-foreground ml-1">討論</span>
                  </div>
                </div>
                <SubscriptionButton
                  itemType={activeSubtopic ? "subtopic" : "topic"}
                  itemId={activeSubtopic
                    ? topic.subtopics?.find(sub => sub.name === activeSubtopic)?.id || ""
                    : topic.id
                  }
                  itemName={activeSubtopic || topic.name}
                  showCount={true}
                  size="sm"
                  preloadedSubscriptionStatus={!subscriptionData.isLoading ? subscriptionData.isSubscribed : undefined}
                  preloadedSubscriberCount={!subscriptionData.isLoading ? subscriptionData.subscriberCount : undefined}
                  skipInitialLoad={subscriptionData.isLoading}
                  onSubscriptionChange={(subscribed) => {
                    setSubscriptionData(prev => ({
                      ...prev,
                      isSubscribed: subscribed,
                      subscriberCount: subscribed ? prev.subscriberCount + 1 : Math.max(0, prev.subscriberCount - 1)
                    }))
                  }}
                />
              </div>
            </div>
            <p className="text-muted-foreground">
              {activeSubtopic ? `智能體之間的通訊和協作機制，是多智能體系統的核心技術` : topic.description}
            </p>
          </div>

          {/* 子主題篩選列 */}
          <div className="flex flex-wrap items-center gap-3">
            <span className="text-sm font-medium text-muted-foreground">
              {activeSubtopic ? "切換子主題:" : "可選子主題:"}
            </span>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={!activeSubtopic ? "default" : "outline"}
                className="cursor-pointer py-1.5 px-3 text-sm"
                onClick={() => handleSubtopicClick(null)}
              >
                全部
              </Badge>
              {topic.subtopics?.map((subtopic) => (
                <Badge
                  key={subtopic.id}
                  variant={activeSubtopic === subtopic.name ? "default" : "outline"}
                  className="cursor-pointer py-1.5 px-3 text-sm"
                  onClick={() => handleSubtopicClick(subtopic.name === activeSubtopic ? null : subtopic.name)}
                >
                  {subtopic.name}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Sticky 區域 - 移出限制性容器，覆蓋整個視窗寬度 */}
      <div
        className="sticky-container w-full"
        style={{
          position: "sticky",
          top: 0,
          zIndex: 50,
          backgroundColor: isSticky ? "rgba(255, 255, 255, 0.95)" : "transparent",
          backdropFilter: isSticky ? "blur(8px)" : "none",
          borderBottom: isSticky ? "1px solid rgba(0,0,0,0.05)" : "none",
          transition: "all 0.3s ease-in-out",
        }}
      >
        <div
          className="max-w-[1400px] mx-auto px-4"
          style={{
            padding: isSticky ? "0.75rem 1rem" : "1rem 1rem",
            transition: "all 0.3s ease-in-out",
          }}
        >
          {/* 麵包屑導航 - 只在 sticky 時顯示 */}
          {isSticky && (
            <nav className="flex items-center text-sm text-muted-foreground mb-3">
              <Link href="/" className="hover:text-foreground flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                  />
                </svg>
                首頁
              </Link>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mx-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <Link href={`/topic/${topic.slug}`} className="hover:text-foreground px-2 py-1 -mx-2 -my-1 rounded-md">
                {topic.name}
              </Link>
              {activeSubtopic && (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mx-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <Link
                    href={`/topic/${topic.slug}/${encodeURIComponent(
                      topic.subtopics?.find((sub) => sub.name === activeSubtopic)?.slug || activeSubtopic.toLowerCase(),
                    )}`}
                    className="font-medium text-foreground px-2 py-1 -mx-2 -my-1 rounded-md"
                  >
                    #{activeSubtopic}
                  </Link>
                </>
              )}
            </nav>
          )}

          {/* Tabs with proper structure */}
          <Tabs defaultValue="viewpoints" value={activeTab} onValueChange={setActiveTab} className="w-full">
            {/* Tabs and search bar */}
            <div className="flex justify-between items-center border-b pb-2 mb-2">
              <TabsList className="bg-transparent h-auto p-0">
                <TabsTrigger
                  value="viewpoints"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-2 bg-transparent"
                >
                  觀點卡
                </TabsTrigger>
                <TabsTrigger
                  value="discussions"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-2 bg-transparent"
                >
                  討論區
                </TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2">
                <div className="relative w-64">
                  <Input placeholder={activeTab === "viewpoints" ? "搜尋觀點..." : "搜尋討論..."} className="pl-8" />
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <ContentFilter
                  filterState={filterState}
                  filterActions={filterActions}
                  availableTopics={availableTopics}
                  availableSubtopics={availableSubtopics}
                  isLoadingFilters={isLoadingFilters}
                  buttonText=""
                  buttonVariant="outline"
                  buttonSize="icon"
                  showFilterCount={true}
                  popoverAlign="end"
                  popoverWidth="w-80"
                />
                <Button className="gap-2">
                  <FileText className="h-4 w-4" />
                  投稿觀點
                </Button>
              </div>
            </div>

            {/* 顯示已選擇的過濾器 */}
            {hasActiveFilters && (
              <div className="pb-2">
                <FilterTags
                  filterState={filterState}
                  filterActions={filterActions}
                  availableTopics={availableTopics}
                  availableSubtopics={availableSubtopics}
                  showClearAll={true}
                />
              </div>
            )}
          </Tabs>
        </div>
      </div>

      {/* 內容區域 */}
      <div className="max-w-[1400px] mx-auto px-4">
        <Tabs defaultValue="viewpoints" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsContent value="viewpoints" className="mt-0 space-y-4">
            <TopicDataLoader
              topicId={topic.id}
              activeSubtopic={activeSubtopic}
              activeSubtopicId={topic.subtopics?.find((s) => s.name === activeSubtopic)?.id || null}
              activeTab="viewpoints"
              filterState={filterState}
              onStatsUpdate={setStats}
            />
          </TabsContent>

          <TabsContent value="discussions" className="mt-2 space-y-4">
            <TopicDataLoader
              topicId={topic.id}
              activeSubtopic={activeSubtopic}
              activeSubtopicId={topic.subtopics?.find((s) => s.name === activeSubtopic)?.id || null}
              activeTab="discussions"
              filterState={filterState}
              onStatsUpdate={setStats}
            />
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}
