/**
 * 權限系統測試腳本
 * 用於測試角色權限系統是否正常工作
 */

const { createClient } = require('@supabase/supabase-js')

// 從環境變數獲取 Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 請設置 NEXT_PUBLIC_SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 環境變數')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testPermissionSystem() {
  console.log('🧪 開始測試權限系統...\n')

  try {
    // 1. 測試角色表是否存在
    console.log('1️⃣ 測試角色表...')
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*')
      .order('level', { ascending: false })

    if (rolesError) {
      console.error('❌ 角色表查詢失敗:', rolesError.message)
      return
    }

    console.log('✅ 角色表查詢成功')
    roles.forEach(role => {
      console.log(`   - ${role.name} (${role.role_key}): 等級 ${role.level}`)
    })
    console.log()

    // 2. 測試權限表是否存在
    console.log('2️⃣ 測試權限表...')
    const { data: permissions, error: permissionsError } = await supabase
      .from('permissions')
      .select('*')
      .order('category', { ascending: true })

    if (permissionsError) {
      console.error('❌ 權限表查詢失敗:', permissionsError.message)
      return
    }

    console.log('✅ 權限表查詢成功')
    const permissionsByCategory = permissions.reduce((acc, perm) => {
      if (!acc[perm.category]) acc[perm.category] = []
      acc[perm.category].push(perm)
      return acc
    }, {})

    Object.entries(permissionsByCategory).forEach(([category, perms]) => {
      console.log(`   ${category.toUpperCase()}:`)
      perms.forEach(perm => {
        console.log(`     - ${perm.name} (${perm.permission_key})`)
      })
    })
    console.log()

    // 3. 測試角色權限關聯
    console.log('3️⃣ 測試角色權限關聯...')
    const { data: rolePermissions, error: rolePermError } = await supabase
      .from('role_permissions')
      .select(`
        role_key,
        permission_key,
        roles:role_key (name),
        permissions:permission_key (name)
      `)

    if (rolePermError) {
      console.error('❌ 角色權限關聯查詢失敗:', rolePermError.message)
      return
    }

    console.log('✅ 角色權限關聯查詢成功')
    const permissionsByRole = rolePermissions.reduce((acc, rp) => {
      if (!acc[rp.role_key]) acc[rp.role_key] = []
      acc[rp.role_key].push(rp.permissions.name)
      return acc
    }, {})

    Object.entries(permissionsByRole).forEach(([roleKey, perms]) => {
      const role = roles.find(r => r.role_key === roleKey)
      console.log(`   ${role?.name || roleKey}:`)
      perms.forEach(perm => {
        console.log(`     - ${perm}`)
      })
    })
    console.log()

    // 4. 測試權限檢查函數
    console.log('4️⃣ 測試權限檢查函數...')
    
    // 獲取第一個用戶來測試
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id, name, email')
      .limit(1)

    if (usersError || !users || users.length === 0) {
      console.log('⚠️  沒有找到測試用戶，跳過權限函數測試')
    } else {
      const testUser = users[0]
      console.log(`   使用測試用戶: ${testUser.name} (${testUser.email})`)

      // 測試權限檢查函數
      const { data: hasPermission, error: permCheckError } = await supabase
        .rpc('check_user_permission', {
          user_id: testUser.id,
          permission_key: 'view_admin_panel'
        })

      if (permCheckError) {
        console.error('❌ 權限檢查函數測試失敗:', permCheckError.message)
      } else {
        console.log(`   ✅ 權限檢查函數正常工作 (結果: ${hasPermission})`)
      }
    }
    console.log()

    // 5. 測試索引是否存在
    console.log('5️⃣ 測試數據庫索引...')
    const { data: indexes, error: indexError } = await supabase
      .rpc('pg_indexes', {})
      .select('indexname, tablename')
      .like('indexname', 'idx_%')

    if (indexError) {
      console.log('⚠️  無法查詢索引信息 (可能需要更高權限)')
    } else {
      const roleIndexes = indexes?.filter(idx => 
        idx.tablename.includes('role') || 
        idx.tablename.includes('moderator')
      ) || []
      
      if (roleIndexes.length > 0) {
        console.log('✅ 找到相關索引:')
        roleIndexes.forEach(idx => {
          console.log(`   - ${idx.indexname} on ${idx.tablename}`)
        })
      } else {
        console.log('⚠️  沒有找到相關索引')
      }
    }
    console.log()

    // 6. 測試清理過期角色函數
    console.log('6️⃣ 測試清理過期角色函數...')
    const { data: cleanupResult, error: cleanupError } = await supabase
      .rpc('cleanup_expired_roles')

    if (cleanupError) {
      console.error('❌ 清理過期角色函數測試失敗:', cleanupError.message)
    } else {
      console.log('✅ 清理過期角色函數正常工作')
      console.log(`   清理結果: ${JSON.stringify(cleanupResult)}`)
    }
    console.log()

    // 7. 測試 RLS 設定
    console.log('7️⃣ 測試 RLS 設定...')
    const { data: rlsStatus, error: rlsError } = await supabase
      .from('pg_tables')
      .select('tablename, rowsecurity')
      .in('tablename', ['roles', 'permissions', 'profile_roles', 'topic_moderators', 'subtopic_moderators'])

    if (rlsError) {
      console.log('⚠️  無法查詢 RLS 狀態 (可能需要更高權限)')
    } else {
      console.log('✅ RLS 狀態查詢成功:')
      rlsStatus?.forEach(table => {
        console.log(`   - ${table.tablename}: ${table.rowsecurity ? '已啟用' : '未啟用'}`)
      })
    }
    console.log()

    console.log('🎉 權限系統測試完成！')
    console.log('\n📋 測試總結:')
    console.log(`   - 角色數量: ${roles.length}`)
    console.log(`   - 權限數量: ${permissions.length}`)
    console.log(`   - 角色權限關聯數量: ${rolePermissions.length}`)
    console.log(`   - 清理過期角色: ${cleanupResult ? '正常' : '異常'}`)
    console.log('\n🔒 安全性改進:')
    console.log('   - ✅ 所有函數使用 SECURITY DEFINER')
    console.log('   - ✅ 設定 search_path = public')
    console.log('   - ✅ 查詢函數標記為 STABLE')
    console.log('   - ✅ 啟用 Row Level Security')
    console.log('   - ✅ 改進權限檢查邏輯')
    console.log('   - ✅ 清理函數返回詳細結果')

  } catch (error) {
    console.error('❌ 測試過程中發生錯誤:', error)
  }
}

// 執行測試
testPermissionSystem().then(() => {
  console.log('\n✨ 測試腳本執行完成')
  process.exit(0)
}).catch(error => {
  console.error('❌ 測試腳本執行失敗:', error)
  process.exit(1)
})
